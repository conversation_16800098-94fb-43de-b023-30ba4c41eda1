# Development Docker Compose Configuration
version: '3.8'

services:
  # Mobile Carwash API Server
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: mobile-carwash-api-dev
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - PORT=8080
      - DB_URI=********************************************************/mobile_carwash_dev
      - JWT_SECRET=your-super-secret-jwt-key-for-development
      - JWT_EXPIRES_IN=7d
      - STRIPE_SECRET_KEY=sk_test_your_stripe_test_secret_key
    volumes:
      # Mount source code for hot reload in development
      - ./src:/app/src:ro
      - ./package.json:/app/package.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      # Exclude node_modules to avoid conflicts
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: mobile-carwash-postgres-dev
    environment:
      - POSTGRES_DB=mobile_carwash_dev
      - POSTGRES_USER=carwash_user
      - POSTGRES_PASSWORD=carwash_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U carwash_user -d mobile_carwash_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for Session Storage (Optional)
  redis:
    image: redis:7-alpine
    container_name: mobile-carwash-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # pgAdmin for Database Management (Development only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: mobile-carwash-pgadmin-dev
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - carwash-network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local

networks:
  carwash-network:
    driver: bridge
    name: mobile-carwash-network

# Docker Compose for Deployment (uses pre-built images)
version: "3.8"

services:
  # Mobile Carwash API Server - Uses pre-built image
  app:
    image: mobile-carwash:latest
    container_name: mobile-carwash-api-cloud
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=8080
      # Use cloud database URL from environment
      - DB_URI=${DB_URI}
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-for-development}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - STRIPE_CURRENCY=${STRIPE_CURRENCY}
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/api/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M
        reservations:
          cpus: "0.5"
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for Session Storage (Optional)
  redis:
    image: redis:7-alpine
    container_name: mobile-carwash-redis-cloud
    ports:
      - "6379:6379"
    volumes:
      - redis_cloud_data:/data
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.25"
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

volumes:
  redis_cloud_data:
    driver: local

networks:
  carwash-network:
    driver: bridge
    name: mobile-carwash-network-cloud

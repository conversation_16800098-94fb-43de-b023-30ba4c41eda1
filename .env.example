# Environment Configuration Example
# Copy this file to .env and update the values

# Application Configuration
NODE_ENV=development
PORT=8080

# Database Configuration
# Choose ONE of the following database setups:

# Option 1: Local PostgreSQL (Docker Compose)
# DB_URI=********************************************************/mobile_carwash_dev

# Option 2: Supabase Cloud Database (Recommended)
DB_URI=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres


# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Stripe Configuration
# Test keys for development
STRIPE_SECRET_KEY=sk_test_your_stripe_test_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_CURRENCY=bgn

# For production, use live keys
# STRIPE_SECRET_KEY=sk_live_your_stripe_live_secret_key_here
# STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_live_publishable_key_here

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379

# Email Configuration (Optional - for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Application URLs
CLIENT_URL=http://localhost:3000
SERVER_URL=http://localhost:8080

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Business Configuration
BUSINESS_HOURS_START=08:00
BUSINESS_HOURS_END=22:00
DEFAULT_SERVICE_DURATION=60
BOOKING_ADVANCE_DAYS=30

# Notification Configuration
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
SMS_API_KEY=your-sms-api-key
SMS_FROM_NUMBER=+1234567890

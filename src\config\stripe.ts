import dotenv from "dotenv";

dotenv.config();

export default {
    secretKey: process.env.STRIPE_SECRET_KEY || '',
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
    apiVersion: '2025-05-28.basil' as const,
    currency: process.env.STRIPE_CURRENCY || 'bgn',

    // Validate required environment variables
    validate() {
        if (!this.secretKey) {
            throw new Error('STRIPE_SECRET_KEY is required in environment variables');
        }

        if (!this.publishableKey) {
            console.warn('STRIPE_PUBLISHABLE_KEY is not set in environment variables');
        }

        return true;
    }
};

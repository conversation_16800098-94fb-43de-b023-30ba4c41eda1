# Cloud Database Docker Compose Configuration (Supabase/Cloud DB)
# Use this when connecting to Supabase or other cloud databases
version: '3.8'

services:
  # Mobile Carwash API Server - Cloud Database
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: mobile-carwash-api-cloud
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - PORT=8080
      # Use cloud database URL from environment
      - DB_URI=${DB_URI}
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-for-development}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    volumes:
      # Mount source code for hot reload in development
      - ./src:/app/src:ro
      - ./package.json:/app/package.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      # Exclude node_modules to avoid conflicts
      - /app/node_modules
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for Session Storage (Optional)
  redis:
    image: redis:7-alpine
    container_name: mobile-carwash-redis-cloud
    ports:
      - "6379:6379"
    volumes:
      - redis_cloud_data:/data
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # pgAdmin for Database Management (Optional - for Supabase management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: mobile-carwash-pgadmin-cloud
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin_cloud_data:/var/lib/pgadmin
    networks:
      - carwash-network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  redis_cloud_data:
    driver: local
  pgadmin_cloud_data:
    driver: local

networks:
  carwash-network:
    driver: bridge
    name: mobile-carwash-network-cloud

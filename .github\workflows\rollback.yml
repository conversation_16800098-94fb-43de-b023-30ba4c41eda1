name: Manual Rollback

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
        - development
        - staging
      version:
        description: 'Specific version to rollback to (optional - leave empty for last known good)'
        required: false
        type: string
      confirm:
        description: 'Type "ROLLBACK" to confirm this action'
        required: true
        type: string

env:
  DEPLOYMENT_PATH: /root/mobile_carwash_server

jobs:
  validate:
    name: Validate Rollback Request
    runs-on: ubuntu-latest
    
    steps:
    - name: Validate confirmation
      if: github.event.inputs.confirm != 'ROLLBACK'
      run: |
        echo "❌ Rollback not confirmed. You must type 'ROLLBACK' to proceed."
        exit 1
        
    - name: Validate environment
      run: |
        echo "✅ Rollback confirmed for ${{ github.event.inputs.environment }} environment"
        if [ -n "${{ github.event.inputs.version }}" ]; then
          echo "🎯 Target version: ${{ github.event.inputs.version }}"
        else
          echo "🎯 Target: Last known good version"
        fi

  rollback-dev:
    name: Rollback Development
    runs-on: ubuntu-latest
    needs: validate
    if: github.event.inputs.environment == 'development'
    environment: development
    
    steps:
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.DEV_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.DEV_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: List available versions
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          echo "📦 Available Docker images:"
          sudo docker images mobile-carwash --format "table {{.Tag}}\t{{.CreatedAt}}" | head -10
          
          echo ""
          echo "💾 Available backups:"
          if [ -d "backups" ]; then
            ls -la backups/*.tar.gz 2>/dev/null | tail -10 || echo "No backups found"
          else
            echo "No backup directory found"
          fi
          
          echo ""
          if [ -f "backups/last-version.txt" ]; then
            echo "🔄 Last known good version: $(cat backups/last-version.txt)"
          else
            echo "⚠️ No last known good version recorded"
          fi
        EOF
        
    - name: Perform rollback
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          set -e
          cd ${{ env.DEPLOYMENT_PATH }}
          
          echo "🔄 Starting rollback process..."
          
          # Determine target version
          TARGET_VERSION="${{ github.event.inputs.version }}"
          
          if [ -z "$TARGET_VERSION" ]; then
            if [ -f "backups/last-version.txt" ]; then
              TARGET_VERSION=$(cat backups/last-version.txt)
              echo "📋 Using last known good version: $TARGET_VERSION"
            else
              # Get the second most recent image (excluding latest)
              TARGET_VERSION=$(sudo docker images mobile-carwash --format '{{.Tag}}' | grep -v latest | head -2 | tail -1)
              echo "📋 Using previous image version: $TARGET_VERSION"
            fi
          else
            echo "📋 Using specified version: $TARGET_VERSION"
          fi
          
          if [ -z "$TARGET_VERSION" ] || [ "$TARGET_VERSION" = "latest" ]; then
            echo "❌ Could not determine rollback version"
            exit 1
          fi
          
          # Stop current services
          echo "⏹️ Stopping current services..."
          sudo docker-compose -f docker-compose.deploy.yml down || true
          
          # Check if target image exists
          if ! sudo docker images mobile-carwash:$TARGET_VERSION --format '{{.Tag}}' | grep -q $TARGET_VERSION; then
            echo "🔍 Target image not found, looking for backup..."
            
            BACKUP_FILE=$(ls backups/*$TARGET_VERSION*.tar.gz 2>/dev/null | head -1)
            if [ -f "$BACKUP_FILE" ]; then
              echo "📦 Loading backup: $BACKUP_FILE"
              sudo docker load < "$BACKUP_FILE"
            else
              echo "❌ No backup found for version $TARGET_VERSION"
              exit 1
            fi
          fi
          
          # Tag rollback version as latest
          echo "🏷️ Tagging version $TARGET_VERSION as latest..."
          sudo docker tag mobile-carwash:$TARGET_VERSION mobile-carwash:latest
          
          # Start services with rollback version
          echo "🚀 Starting services with rollback version..."
          sudo docker-compose -f docker-compose.deploy.yml up -d
          
          # Record rollback
          mkdir -p backups
          echo "$(date): Rolled back to $TARGET_VERSION" >> backups/rollback-history.txt
          echo "$TARGET_VERSION" > backups/current-version.txt
          
          echo "✅ Rollback to version $TARGET_VERSION completed!"
        EOF
        
    - name: Verify rollback
      run: |
        echo "⏳ Waiting for services to start..."
        sleep 30
        
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          # Check container status
          echo "🐳 Container status:"
          sudo docker-compose -f docker-compose.deploy.yml ps
          
          # Test health endpoint
          echo "🏥 Testing health endpoint..."
          if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
            echo "✅ Health check passed - rollback successful!"
          else
            echo "❌ Health check failed - rollback may have issues"
            exit 1
          fi
        EOF

  rollback-staging:
    name: Rollback Staging
    runs-on: ubuntu-latest
    needs: validate
    if: github.event.inputs.environment == 'staging'
    environment: staging
    
    steps:
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.STAGING_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: List available versions
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          echo "📦 Available Docker images:"
          sudo docker images mobile-carwash --format "table {{.Tag}}\t{{.CreatedAt}}" | head -10
          
          echo ""
          echo "💾 Available backups:"
          if [ -d "backups" ]; then
            ls -la backups/*.tar.gz 2>/dev/null | tail -10 || echo "No backups found"
          else
            echo "No backup directory found"
          fi
          
          echo ""
          if [ -f "backups/last-version.txt" ]; then
            echo "🔄 Last known good version: $(cat backups/last-version.txt)"
          else
            echo "⚠️ No last known good version recorded"
          fi
        EOF
        
    - name: Create pre-rollback backup
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          echo "💾 Creating pre-rollback backup..."
          mkdir -p backups
          
          # Get current version
          CURRENT_VERSION=$(sudo docker images mobile-carwash --format '{{.Tag}}' | grep -v latest | head -1)
          if [ ! -z "$CURRENT_VERSION" ]; then
            sudo docker save mobile-carwash:$CURRENT_VERSION | gzip > backups/pre-rollback-$CURRENT_VERSION-$(date +%Y%m%d-%H%M%S).tar.gz
            echo "✅ Pre-rollback backup created"
          fi
        EOF
        
    - name: Perform rollback
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          set -e
          cd ${{ env.DEPLOYMENT_PATH }}
          
          echo "🔄 Starting rollback process..."
          
          # Determine target version
          TARGET_VERSION="${{ github.event.inputs.version }}"
          
          if [ -z "$TARGET_VERSION" ]; then
            if [ -f "backups/last-version.txt" ]; then
              TARGET_VERSION=$(cat backups/last-version.txt)
              echo "📋 Using last known good version: $TARGET_VERSION"
            else
              # Get the second most recent image (excluding latest)
              TARGET_VERSION=$(sudo docker images mobile-carwash --format '{{.Tag}}' | grep -v latest | head -2 | tail -1)
              echo "📋 Using previous image version: $TARGET_VERSION"
            fi
          else
            echo "📋 Using specified version: $TARGET_VERSION"
          fi
          
          if [ -z "$TARGET_VERSION" ] || [ "$TARGET_VERSION" = "latest" ]; then
            echo "❌ Could not determine rollback version"
            exit 1
          fi
          
          # Stop current services
          echo "⏹️ Stopping current services..."
          sudo docker-compose -f docker-compose.deploy.yml down || true
          
          # Check if target image exists
          if ! sudo docker images mobile-carwash:$TARGET_VERSION --format '{{.Tag}}' | grep -q $TARGET_VERSION; then
            echo "🔍 Target image not found, looking for backup..."
            
            BACKUP_FILE=$(ls backups/*$TARGET_VERSION*.tar.gz 2>/dev/null | head -1)
            if [ -f "$BACKUP_FILE" ]; then
              echo "📦 Loading backup: $BACKUP_FILE"
              sudo docker load < "$BACKUP_FILE"
            else
              echo "❌ No backup found for version $TARGET_VERSION"
              exit 1
            fi
          fi
          
          # Tag rollback version as latest
          echo "🏷️ Tagging version $TARGET_VERSION as latest..."
          sudo docker tag mobile-carwash:$TARGET_VERSION mobile-carwash:latest
          
          # Start services with rollback version
          echo "🚀 Starting services with rollback version..."
          sudo docker-compose -f docker-compose.deploy.yml up -d
          
          # Record rollback
          mkdir -p backups
          echo "$(date): Rolled back to $TARGET_VERSION" >> backups/rollback-history.txt
          echo "$TARGET_VERSION" > backups/current-version.txt
          
          echo "✅ Rollback to version $TARGET_VERSION completed!"
        EOF
        
    - name: Verify rollback
      run: |
        echo "⏳ Waiting for services to start..."
        sleep 45
        
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          # Check container status
          echo "🐳 Container status:"
          sudo docker-compose -f docker-compose.deploy.yml ps
          
          # Test health endpoint
          echo "🏥 Testing health endpoint..."
          if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
            echo "✅ Health check passed - rollback successful!"
          else
            echo "❌ Health check failed - rollback may have issues"
            exit 1
          fi
          
          # Test additional endpoints
          echo "🔍 Testing additional endpoints..."
          curl -f http://localhost:8080/api/health || echo "⚠️ Health endpoint issue"
          
          echo "📊 Final status check..."
          sudo docker-compose -f docker-compose.deploy.yml logs --tail=20 app
        EOF

  notify:
    name: Notify Rollback Status
    runs-on: ubuntu-latest
    needs: [rollback-dev, rollback-staging]
    if: always()
    
    steps:
    - name: Notify success
      if: (needs.rollback-dev.result == 'success' && github.event.inputs.environment == 'development') || (needs.rollback-staging.result == 'success' && github.event.inputs.environment == 'staging')
      run: |
        echo "✅ Rollback successful!"
        echo "🔄 Environment: ${{ github.event.inputs.environment }}"
        echo "📦 Version: ${{ github.event.inputs.version || 'Last known good' }}"
        echo "👤 Triggered by: ${{ github.actor }}"
        echo "🕐 Completed at: $(date)"
        
    - name: Notify failure
      if: (needs.rollback-dev.result == 'failure' && github.event.inputs.environment == 'development') || (needs.rollback-staging.result == 'failure' && github.event.inputs.environment == 'staging')
      run: |
        echo "❌ Rollback failed!"
        echo "🔄 Environment: ${{ github.event.inputs.environment }}"
        echo "📦 Target version: ${{ github.event.inputs.version || 'Last known good' }}"
        echo "👤 Triggered by: ${{ github.actor }}"
        echo "🔍 Check the logs for details"
        echo "⚠️ Manual intervention may be required"

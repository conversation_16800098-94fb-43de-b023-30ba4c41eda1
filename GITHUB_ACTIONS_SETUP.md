# GitHub Actions CI/CD Setup Guide

This guide covers the complete setup of CI/CD pipelines using GitHub Actions for the Mobile Carwash Server with DigitalOcean droplets.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [GitHub Secrets Configuration](#github-secrets-configuration)
4. [Workflow Files](#workflow-files)
5. [DigitalOcean Server Setup](#digitalocean-server-setup)
6. [Deployment Process](#deployment-process)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Troubleshooting](#troubleshooting)

## 🎯 Overview

The GitHub Actions setup includes three main workflows:

- **Development Deployment** (`deploy-dev.yml`) - Deploys to dev environment on `develop` branch pushes
- **Staging Deployment** (`deploy-staging.yml`) - Deploys to staging environment on `main` branch pushes
- **Health Check** (`health-check.yml`) - Manual and scheduled health monitoring

### Key Features

✅ **Automated Testing** - TypeScript compilation, security audits, and tests  
✅ **Docker Image Building** - Multi-stage builds with vulnerability scanning  
✅ **Zero-Downtime Deployment** - Rolling deployments with health checks  
✅ **Automatic Rollback** - Rollback on deployment failure  
✅ **Security Scanning** - Trivy vulnerability scanning  
✅ **Health Monitoring** - Automated health checks and performance tests  

## 🔧 Prerequisites

### Required Accounts and Services
- GitHub repository with Actions enabled
- DigitalOcean account with droplets
- Supabase database
- Stripe account

### DigitalOcean Droplets
Create the following droplets:

**Development Server:**
- Size: 1 GB RAM, 1 vCPU, 25 GB SSD
- OS: Ubuntu 22.04 LTS

**Staging Server:**
- Size: 2 GB RAM, 1 vCPU, 50 GB SSD
- OS: Ubuntu 22.04 LTS

## 🔐 GitHub Secrets Configuration

Go to your GitHub repository → Settings → Secrets and variables → Actions

### Development Environment Secrets

```
DEV_SERVER_IP=your.dev.server.ip
DEV_SERVER_USER=root
DEV_SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
[Your SSH private key content]
-----END OPENSSH PRIVATE KEY-----

DEV_DB_URI=postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres
DEV_JWT_SECRET=your-development-jwt-secret-key
DEV_STRIPE_SECRET_KEY=sk_test_your_development_stripe_key
```

### Staging Environment Secrets

```
STAGING_SERVER_IP=your.staging.server.ip
STAGING_SERVER_USER=root
STAGING_SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
[Your SSH private key content]
-----END OPENSSH PRIVATE KEY-----

STAGING_DB_URI=postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres
STAGING_JWT_SECRET=your-staging-jwt-secret-key
STAGING_STRIPE_SECRET_KEY=sk_test_your_staging_stripe_key
```

### How to Generate SSH Keys

```bash
# Generate SSH key pair
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/github_actions

# Copy public key to your servers
ssh-copy-id -i ~/.ssh/github_actions.pub <EMAIL>
ssh-copy-id -i ~/.ssh/github_actions.pub <EMAIL>

# Copy private key content for GitHub Secrets
cat ~/.ssh/github_actions
```

## 📁 Workflow Files

### 1. Development Deployment (`.github/workflows/deploy-dev.yml`)

**Triggers:**
- Push to `develop` branch
- Pull requests to `develop` branch

**Process:**
1. Run tests and type checking
2. Build Docker image
3. Deploy to development server
4. Run health checks
5. Cleanup old images

### 2. Staging Deployment (`.github/workflows/deploy-staging.yml`)

**Triggers:**
- Push to `main` branch
- Manual workflow dispatch

**Process:**
1. Run comprehensive tests
2. Security vulnerability scanning
3. Build and scan Docker image
4. Create backup of current deployment
5. Deploy to staging server
6. Run health checks and verification
7. Automatic rollback on failure

### 3. Health Check (`.github/workflows/health-check.yml`)

**Triggers:**
- Manual workflow dispatch
- Scheduled (every 6 hours)

**Features:**
- Environment-specific health checks
- System resource monitoring
- API endpoint testing
- Performance testing
- Log analysis

## 🖥️ DigitalOcean Server Setup

### 1. Initial Server Configuration

Run these commands on both development and staging servers:

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create deployment directory
sudo mkdir -p /opt/mobile-carwash
sudo chown $USER:$USER /opt/mobile-carwash

# Install additional tools
sudo apt install -y curl wget htop tree vim
```

### 2. Configure Firewall

```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp
```

### 3. Setup SSH Access

```bash
# Add your GitHub Actions public key to authorized_keys
echo "your-github-actions-public-key" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

## 🚀 Deployment Process

### Development Deployment

1. **Push to develop branch:**
   ```bash
   git checkout develop
   git add .
   git commit -m "Your changes"
   git push origin develop
   ```

2. **Automatic process:**
   - Tests run automatically
   - Docker image builds
   - Deploys to development server
   - Health checks verify deployment

### Staging Deployment

1. **Merge to main branch:**
   ```bash
   git checkout main
   git merge develop
   git push origin main
   ```

2. **Automatic process:**
   - Comprehensive testing
   - Security scanning
   - Backup creation
   - Deployment to staging
   - Health verification
   - Rollback on failure

### Manual Deployment

You can trigger deployments manually:

1. Go to GitHub → Actions
2. Select "Deploy to Staging"
3. Click "Run workflow"
4. Choose options and run

## 📊 Monitoring and Maintenance

### Health Checks

**Automatic monitoring:**
- Runs every 6 hours
- Checks system resources
- Verifies API endpoints
- Monitors container status

**Manual health checks:**
1. Go to GitHub → Actions
2. Select "Health Check"
3. Choose environment and run

### Log Monitoring

**View deployment logs:**
- GitHub → Actions → Select workflow run
- Click on specific job to view logs

**Server logs:**
```bash
# SSH to server
ssh <EMAIL>

# View application logs
cd /opt/mobile-carwash
sudo docker-compose logs -f app

# View system logs
sudo journalctl -f
```

### Backup Management

**Automatic backups:**
- Created before each deployment
- Stored in `/opt/mobile-carwash/backups/`
- Old backups cleaned automatically

**Manual backup:**
```bash
cd /opt/mobile-carwash
sudo docker save mobile-carwash:latest | gzip > manual-backup-$(date +%Y%m%d).tar.gz
```

## 🔧 Troubleshooting

### Common Issues

**1. SSH Connection Failed**
```bash
# Check SSH key format in GitHub Secrets
# Ensure key starts with -----BEGIN OPENSSH PRIVATE KEY-----
# Verify server IP and user are correct
```

**2. Docker Build Failed**
```bash
# Check Dockerfile syntax
# Verify all dependencies are available
# Check for TypeScript compilation errors
```

**3. Health Check Failed**
```bash
# SSH to server and check manually
ssh <EMAIL>
cd /opt/mobile-carwash
sudo docker-compose ps
sudo docker-compose logs app
```

**4. Deployment Stuck**
```bash
# Check if previous containers are still running
sudo docker ps -a
sudo docker-compose down
sudo docker-compose up -d
```

### Rollback Process

**Automatic rollback** happens on staging deployment failure.

**Manual rollback:**
```bash
# SSH to server
ssh <EMAIL>
cd /opt/mobile-carwash

# Check available backups
ls -la backups/

# Load previous version
sudo docker load < backups/mobile-carwash-[version].tar.gz
sudo docker tag mobile-carwash:[version] mobile-carwash:latest
sudo docker-compose up -d
```

### Performance Issues

**Monitor resources:**
```bash
# System resources
htop
df -h
free -h

# Docker resources
sudo docker stats

# Application performance
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8080/api/health
```

## 🔒 Security Best Practices

1. **Secrets Management:**
   - Use GitHub Secrets for all sensitive data
   - Rotate secrets regularly
   - Use different secrets for each environment

2. **Server Security:**
   - Keep servers updated
   - Use firewall rules
   - Monitor access logs
   - Use non-root users when possible

3. **Container Security:**
   - Scan images for vulnerabilities
   - Use minimal base images
   - Run containers as non-root users
   - Keep dependencies updated

## 📞 Support

**For deployment issues:**
1. Check GitHub Actions logs
2. Review server logs
3. Run manual health checks
4. Check troubleshooting section

**Emergency contacts:**
- Development team: [<EMAIL>]
- Infrastructure: [<EMAIL>]

---

**Note:** This setup assumes Ubuntu 22.04 LTS servers. Adjust commands for other distributions as needed.

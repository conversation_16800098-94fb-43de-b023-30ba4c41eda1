name: Deploy to Staging

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  DOCKER_IMAGE: mobile-carwash
  DEPLOYMENT_PATH: /root/mobile_carwash_server

jobs:
  test:
    name: Test Application
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript type check
      run: npx tsc --noEmit
      
    - name: Run tests
      run: npm test || echo "No tests configured yet"
      
    - name: Run security audit
      run: npm audit --audit-level high
      
    - name: Run lint (if configured)
      run: npm run lint || echo "No linting configured yet"

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Generate version tag
      id: version
      run: |
        # Create version tag from git info
        VERSION="staging-$(date +%Y%m%d)-${GITHUB_SHA:0:7}"
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "short_sha=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT
        
    - name: Build Docker image
      run: |
        docker build \
          --target production \
          --tag ${{ env.DOCKER_IMAGE }}:${{ steps.version.outputs.version }} \
          --tag ${{ env.DOCKER_IMAGE }}:staging-latest \
          --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
          --build-arg VCS_REF=${{ steps.version.outputs.short_sha }} \
          --build-arg VERSION=${{ steps.version.outputs.version }} \
          .
          
    - name: Test Docker image
      run: |
        # Test that the image can start
        docker run --rm -d \
          --name test-container \
          -e NODE_ENV=production \
          -e PORT=8080 \
          -e DB_URI="postgresql://test:test@localhost:5432/test" \
          -e JWT_SECRET="test-secret" \
          -e STRIPE_SECRET_KEY="sk_test_test" \
          -p 8080:8080 \
          ${{ env.DOCKER_IMAGE }}:${{ steps.version.outputs.version }}
          
        # Wait for container to start
        sleep 15
        
        # Check if container is running
        docker ps | grep test-container
        
        # Test health endpoint (basic check)
        timeout 30 bash -c 'until curl -f http://localhost:8080/api/health 2>/dev/null; do sleep 2; done' || echo "Health check timeout (expected without real DB)"
        
        # Stop test container
        docker stop test-container
        
    - name: Scan Docker image for vulnerabilities
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.DOCKER_IMAGE }}:${{ steps.version.outputs.version }}
        format: 'table'
        exit-code: '1'
        ignore-unfixed: true
        severity: 'CRITICAL,HIGH'
        
    - name: Save Docker image
      run: |
        docker save ${{ env.DOCKER_IMAGE }}:${{ steps.version.outputs.version }} | gzip > mobile-carwash-staging.tar.gz
        
    - name: Upload Docker image artifact
      uses: actions/upload-artifact@v4
      with:
        name: docker-image-staging
        path: mobile-carwash-staging.tar.gz
        retention-days: 7
        
    outputs:
      version: ${{ steps.version.outputs.version }}

  deploy:
    name: Deploy to Staging Server
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' && (github.event_name == 'push' || github.event.inputs.force_deploy == 'true')
    
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Docker image artifact
      uses: actions/download-artifact@v4
      with:
        name: docker-image-staging
        
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.STAGING_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Create backup of current deployment
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }} || exit 0
          
          # Create backup directory
          sudo mkdir -p backups
          
          # Stop current services for backup
          sudo docker-compose -f docker-compose.prod.yml down || true
          
          # Backup current image if it exists
          CURRENT_VERSION=$(sudo docker images mobile-carwash --format 'table {{.Tag}}' | grep -v TAG | grep -v latest | head -1)
          if [ ! -z "$CURRENT_VERSION" ]; then
            sudo docker save mobile-carwash:$CURRENT_VERSION | gzip > backups/mobile-carwash-$CURRENT_VERSION-$(date +%Y%m%d-%H%M%S).tar.gz
            echo $CURRENT_VERSION > backups/last-version.txt
            echo "Backup created for version: $CURRENT_VERSION"
          fi
        EOF
        
    - name: Copy files to server
      run: |
        # Copy Docker image
        scp -i ~/.ssh/id_rsa mobile-carwash-staging.tar.gz ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }}:/tmp/
        
        # Copy docker-compose file
        scp -i ~/.ssh/id_rsa docker-compose.prod.yml ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }}:/tmp/
        
    - name: Deploy to staging server
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          set -e
          
          # Navigate to deployment directory
          cd ${{ env.DEPLOYMENT_PATH }}
          
          # Create environment file
          sudo tee .env > /dev/null << 'ENVEOF'
        NODE_ENV=production
        PORT=8080
        DB_URI=${{ secrets.STAGING_DB_URI }}
        JWT_SECRET=${{ secrets.STAGING_JWT_SECRET }}
        JWT_EXPIRES_IN=24h
        STRIPE_SECRET_KEY=${{ secrets.STAGING_STRIPE_SECRET_KEY }}
        ENVEOF
          
          # Load new Docker image
          sudo docker load < /tmp/mobile-carwash-staging.tar.gz
          
          # Update docker-compose file
          sudo cp /tmp/docker-compose.prod.yml .
          
          # Tag the new image
          sudo docker tag mobile-carwash:${{ needs.build.outputs.version }} mobile-carwash:latest
          
          # Start services
          sudo docker-compose -f docker-compose.prod.yml up -d
          
          # Clean up temporary files
          rm -f /tmp/mobile-carwash-staging.tar.gz /tmp/docker-compose.prod.yml
          
          # Wait for services to be ready
          echo "Waiting for services to start..."
          sleep 45
          
          # Check if services are running
          sudo docker-compose -f docker-compose.prod.yml ps
        EOF
        
    - name: Health check and verification
      run: |
        # Wait for services to fully start
        sleep 30
        
        # Run comprehensive health check
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          # Test health endpoint
          echo "Testing health endpoint..."
          curl -f http://localhost:8080/api/health || exit 1
          
          # Test API endpoints (basic checks)
          echo "Testing API endpoints..."
          curl -f http://localhost:8080/api/health || exit 1
          
          # Check container status
          echo "Checking container status..."
          sudo docker-compose -f docker-compose.prod.yml ps
          
          # Check logs for any errors
          echo "Checking recent logs..."
          sudo docker-compose -f docker-compose.prod.yml logs --tail=50 app
          
          echo "✅ Staging deployment successful!"
        EOF
        
    - name: Cleanup old Docker images
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          # Keep only the last 5 images
          sudo docker images mobile-carwash --format "table {{.Tag}}" | grep -v TAG | grep -v latest | tail -n +6 | xargs -r sudo docker rmi mobile-carwash: || true
          
          # Clean up unused images and containers
          sudo docker image prune -f
          sudo docker container prune -f
          
          # Clean up old backups (keep last 10)
          find backups -name "*.tar.gz" -type f | sort | head -n -10 | xargs -r rm -f || true
        EOF

  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: [build, deploy]
    if: failure() && needs.deploy.result == 'failure'
    
    steps:
    - name: Setup SSH for rollback
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.STAGING_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Perform rollback
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}
          
          echo "🔄 Performing automatic rollback..."
          
          # Get last known good version
          if [ -f backups/last-version.txt ]; then
            LAST_VERSION=$(cat backups/last-version.txt)
            echo "Rolling back to version: $LAST_VERSION"
            
            # Stop current services
            sudo docker-compose -f docker-compose.prod.yml down
            
            # Load backup if needed
            if ! sudo docker images mobile-carwash:$LAST_VERSION --format '{{.Tag}}' | grep -q $LAST_VERSION; then
              BACKUP_FILE=$(ls backups/*$LAST_VERSION*.tar.gz | head -1)
              if [ -f "$BACKUP_FILE" ]; then
                sudo docker load < $BACKUP_FILE
              fi
            fi
            
            # Tag and start with previous version
            sudo docker tag mobile-carwash:$LAST_VERSION mobile-carwash:latest
            sudo docker-compose -f docker-compose.prod.yml up -d
            
            # Wait and verify
            sleep 30
            curl -f http://localhost:8080/api/health && echo "✅ Rollback successful!" || echo "❌ Rollback failed!"
          else
            echo "❌ No previous version found for rollback"
          fi
        EOF

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [test, security-scan, build, deploy]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Staging deployment successful!"
        echo "🚀 Version: ${{ needs.build.outputs.version }}"
        echo "🌐 Environment: Staging"
        echo "📦 Commit: ${{ github.event.head_commit.message }}"
        echo "🔗 Server: ${{ secrets.STAGING_SERVER_IP }}"
        
    - name: Notify failure
      if: needs.deploy.result == 'failure' || needs.build.result == 'failure' || needs.test.result == 'failure'
      run: |
        echo "❌ Staging deployment failed!"
        echo "🔍 Check the logs for details"
        echo "📦 Commit: ${{ github.event.head_commit.message }}"
        echo "🔄 Automatic rollback attempted"

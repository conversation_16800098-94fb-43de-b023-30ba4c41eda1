import Stripe from "stripe";
import dotenv from "dotenv";

dotenv.config();

class StripeService {
    private stripe: Stripe;

    constructor() {
        const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

        if (!stripeSecretKey) {
            throw new Error(
                "STRIPE_SECRET_KEY is not defined in environment variables"
            );
        }

        this.stripe = new Stripe(stripeSecretKey);
    }

    /**
     * Create a new Stripe customer
     * @param email Customer's email address
     * @param name Customer's full name
     * @param phone Customer's phone number (optional)
     * @param metadata Additional metadata to store with the customer
     * @returns Promise<Stripe.Customer>
     */
    async createCustomer(
        email: string,
        name: string,
        phone?: string,
        metadata?: Record<string, string>
    ): Promise<Stripe.Customer> {
        try {
            const customerData: Stripe.CustomerCreateParams = {
                email,
                name,
                metadata: {
                    source: "mobile_carwash_app",
                    ...metadata,
                },
            };

            if (phone) {
                customerData.phone = phone;
            }

            const customer = await this.stripe.customers.create(customerData);

            console.log(
                `Stripe customer created: ${customer.id} for email: ${email}`
            );
            return customer;
        } catch (error) {
            console.error("Error creating Stripe customer:", error);
            throw new Error(
                `Failed to create Stripe customer: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Retrieve a Stripe customer by ID
     * @param customerId Stripe customer ID
     * @returns Promise<Stripe.Customer>
     */
    async getCustomer(customerId: string): Promise<Stripe.Customer> {
        try {
            const customer = await this.stripe.customers.retrieve(customerId);

            if (customer.deleted) {
                throw new Error("Customer has been deleted");
            }

            return customer as Stripe.Customer;
        } catch (error) {
            console.error("Error retrieving Stripe customer:", error);
            throw new Error(
                `Failed to retrieve Stripe customer: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Update a Stripe customer
     * @param customerId Stripe customer ID
     * @param updateData Data to update
     * @returns Promise<Stripe.Customer>
     */
    async updateCustomer(
        customerId: string,
        updateData: Stripe.CustomerUpdateParams
    ): Promise<Stripe.Customer> {
        try {
            const customer = await this.stripe.customers.update(
                customerId,
                updateData
            );
            console.log(`Stripe customer updated: ${customer.id}`);
            return customer;
        } catch (error) {
            console.error("Error updating Stripe customer:", error);
            throw new Error(
                `Failed to update Stripe customer: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Delete a Stripe customer
     * @param customerId Stripe customer ID
     * @returns Promise<Stripe.DeletedCustomer>
     */
    async deleteCustomer(customerId: string): Promise<Stripe.DeletedCustomer> {
        try {
            const deletedCustomer = await this.stripe.customers.del(customerId);
            console.log(`Stripe customer deleted: ${customerId}`);
            return deletedCustomer;
        } catch (error) {
            console.error("Error deleting Stripe customer:", error);
            throw new Error(
                `Failed to delete Stripe customer: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * List payment methods for a customer
     * @param customerId Stripe customer ID
     * @param type Payment method type (optional, defaults to 'card')
     * @returns Promise<Stripe.PaymentMethod[]>
     */
    async listPaymentMethods(
        customerId: string,
        type: Stripe.PaymentMethodListParams.Type = "card"
    ): Promise<Stripe.PaymentMethod[]> {
        try {
            const paymentMethods = await this.stripe.paymentMethods.list({
                customer: customerId,
                type,
            });

            return paymentMethods.data;
        } catch (error) {
            console.error("Error listing payment methods:", error);
            throw new Error(
                `Failed to list payment methods: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Create a payment intent for a customer with payment method
     * @param amount Amount in cents
     * @param currency Currency code (default: 'bgn')
     * @param customerId Stripe customer ID
     * @param paymentMethodId Payment method ID to use
     * @param metadata Additional metadata
     * @returns Promise<Stripe.PaymentIntent>
     */
    async createPaymentIntentWithPaymentMethod(
        amount: number,
        currency: string = "bgn",
        customerId: string,
        paymentMethodId: string,
        metadata?: Record<string, string>
    ): Promise<Stripe.PaymentIntent> {
        try {
            const paymentIntentData: Stripe.PaymentIntentCreateParams = {
                amount,
                currency,
                customer: customerId,
                payment_method: paymentMethodId,
                off_session: true,
                confirm: true,
            };

            const paymentIntent = await this.stripe.paymentIntents.create(
                paymentIntentData
            );

            console.log(
                `Payment intent created and confirmed: ${paymentIntent.id} for amount: ${amount}`
            );
            return paymentIntent;
        } catch (error) {
            console.error(
                "Error creating payment intent with payment method:",
                error
            );
            throw new Error(
                `Failed to create payment intent: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

     /**
     * Attaches payment method to a customer.
     * @param customerId Stripe customer ID
     * @param paymentMethodId Payment method ID to use
     * @returns Promise<Stripe.PaymentMethod>
     */
    async createPaymentMethod(
        customerId: string,
        paymentMethodId: string
    ): Promise<Stripe.PaymentMethod> {
        try {
            return this.stripe.paymentMethods.attach(paymentMethodId, {
                customer: customerId,
            });
        } catch (error) {
            console.error(
                "Error creating payment intent with payment method:",
                error
            );
            throw new Error(
                `Failed to create payment intent: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Create a payment intent for a customer (for frontend completion)
     * @param amount Amount in cents
     * @param currency Currency code (default: 'bgn')
     * @param customerId Stripe customer ID
     * @param metadata Additional metadata
     * @returns Promise<Stripe.PaymentIntent>
     */
    async createPaymentIntent(
        amount: number,
        currency: string = "bgn",
        customerId?: string,
        metadata?: Record<string, string>
    ): Promise<Stripe.PaymentIntent> {
        try {
            const paymentIntentData: Stripe.PaymentIntentCreateParams = {
                amount,
                currency,
                metadata: {
                    source: "mobile_carwash_app",
                    ...metadata,
                },
            };

            if (customerId) {
                paymentIntentData.customer = customerId;
            }

            const paymentIntent = await this.stripe.paymentIntents.create(
                paymentIntentData
            );

            console.log(
                `Payment intent created: ${paymentIntent.id} for amount: ${amount}`
            );
            return paymentIntent;
        } catch (error) {
            console.error("Error creating payment intent:", error);
            throw new Error(
                `Failed to create payment intent: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Capture a payment intent
     * @param paymentIntentId Payment intent ID to capture
     * @param amountToCapture Optional amount to capture (if different from authorized amount)
     * @returns Promise<Stripe.PaymentIntent>
     */
    async capturePaymentIntent(
        paymentIntentId: string,
        amountToCapture?: number
    ): Promise<Stripe.PaymentIntent> {
        try {
            const captureData: Stripe.PaymentIntentCaptureParams = {};

            if (amountToCapture) {
                captureData.amount_to_capture = amountToCapture;
            }

            const paymentIntent = await this.stripe.paymentIntents.capture(
                paymentIntentId,
                captureData
            );

            console.log(`Payment intent captured: ${paymentIntent.id}`);
            return paymentIntent;
        } catch (error) {
            console.error("Error capturing payment intent:", error);
            throw new Error(
                `Failed to capture payment intent: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Cancel a payment intent
     * @param paymentIntentId Payment intent ID to cancel
     * @returns Promise<Stripe.PaymentIntent>
     */
    async cancelPaymentIntent(
        paymentIntentId: string
    ): Promise<Stripe.PaymentIntent> {
        try {
            const paymentIntent = await this.stripe.paymentIntents.cancel(
                paymentIntentId
            );

            console.log(`Payment intent cancelled: ${paymentIntent.id}`);
            return paymentIntent;
        } catch (error) {
            console.error("Error cancelling payment intent:", error);
            throw new Error(
                `Failed to cancel payment intent: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    /**
     * Get the Stripe instance for advanced operations
     * @returns Stripe instance
     */
    getStripeInstance(): Stripe {
        return this.stripe;
    }
}

// Export a singleton instance
export default new StripeService();

# Supabase + Docker Setup Guide

This guide explains how to use your Docker setup with Supabase cloud database instead of a local PostgreSQL container.

## 🌟 Benefits of Using Supabase with Docker

- **Managed Database**: No need to manage PostgreSQL containers
- **Automatic Backups**: Built-in backup and point-in-time recovery
- **Scalability**: Automatic scaling based on usage
- **Real-time Features**: Built-in real-time subscriptions
- **Dashboard**: Web-based database management
- **Global CDN**: Fast database access worldwide

## 🚀 Quick Setup

### 1. Get Your Supabase Database URL

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create a new project or select existing one
3. Go to **Settings** → **Database**
4. Copy the **Connection string** (URI format)

Example Supabase URL:
```
postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres
```

### 2. Configure Environment Variables

Create your `.env` file:
```bash
cp .env.example .env
```

Update the database configuration in `.env`:
```env
# Supabase Database Configuration
DB_URI=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# Other configurations...
NODE_ENV=development
PORT=8080
JWT_SECRET=your-super-secret-jwt-key
STRIPE_SECRET_KEY=sk_test_your_stripe_key
```

### 3. Start Docker with Cloud Database

Use the cloud-optimized Docker Compose:
```bash
# Start with Supabase
npm run docker:cloud

# Or directly with docker-compose
docker-compose -f docker-compose.cloud.yml up -d
```

### 4. Initialize Database Schema

Run the database initialization:
```bash
# Access the container
docker-compose -f docker-compose.cloud.yml exec app sh

# Initialize database with tables and sample data
npm run init-db
```

## 📋 Available Docker Configurations

### 1. Local Development (with local PostgreSQL)
```bash
npm run docker:dev
```
- Uses local PostgreSQL container
- Includes pgAdmin for database management
- Good for offline development

### 2. Cloud Development (with Supabase)
```bash
npm run docker:cloud
```
- Uses Supabase cloud database
- Only runs application and Redis containers
- Recommended for team development

### 3. Production (with any cloud database)
```bash
npm run docker:prod
```
- Optimized for production deployment
- Uses environment variables for all configuration
- No development tools included

## 🔧 Configuration Comparison

| Feature | Local DB | Supabase | Production |
|---------|----------|----------|------------|
| Database | Local PostgreSQL | Supabase Cloud | Any Cloud DB |
| SSL | Optional | Required | Required |
| Backups | Manual | Automatic | Depends on provider |
| Scaling | Manual | Automatic | Depends on provider |
| Management | pgAdmin | Supabase Dashboard | Provider Dashboard |
| Cost | Free | Free tier + usage | Depends on provider |

## 🛠️ Database Management

### Using Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to **Table Editor** to view/edit data
3. Use **SQL Editor** for custom queries
4. Monitor performance in **Database** section

### Using pgAdmin (Optional)
If you want to use pgAdmin with Supabase:
```bash
# Start with tools profile
docker-compose -f docker-compose.cloud.yml --profile tools up -d

# Access pgAdmin at http://localhost:5050
# Add Supabase server with your connection details
```

### Connection Details for pgAdmin:
- **Host**: `db.[YOUR-PROJECT-REF].supabase.co`
- **Port**: `5432`
- **Database**: `postgres`
- **Username**: `postgres`
- **Password**: `[YOUR-PASSWORD]`
- **SSL Mode**: `Require`

## 🔍 Monitoring and Debugging

### Health Checks
Your application includes health checks that work with Supabase:
```bash
# Check application health
curl http://localhost:8080/api/health

# View detailed logs
npm run docker:cloud:logs
```

### Database Connection Testing
```bash
# Access application container
docker-compose -f docker-compose.cloud.yml exec app sh

# Test database connection
node -e "
const sequelize = require('./dist/config/database').default;
sequelize.authenticate()
  .then(() => console.log('✅ Database connected'))
  .catch(err => console.error('❌ Database error:', err));
"
```

## 🚨 Troubleshooting

### Common Issues

1. **SSL Connection Errors**
   ```
   Error: self signed certificate in certificate chain
   ```
   **Solution**: Ensure `DB_SSL_ENABLED=true` in your `.env` file

2. **Connection Timeout**
   ```
   Error: connect ETIMEDOUT
   ```
   **Solution**: Check your Supabase URL and increase `DB_CONNECTION_TIMEOUT`

3. **Too Many Connections**
   ```
   Error: remaining connection slots are reserved
   ```
   **Solution**: Reduce `DB_POOL_MAX` in your environment variables

4. **Authentication Failed**
   ```
   Error: password authentication failed
   ```
   **Solution**: Verify your Supabase password and URL

### Debug Commands
```bash
# Check container status
docker-compose -f docker-compose.cloud.yml ps

# View application logs
docker-compose -f docker-compose.cloud.yml logs app

# Check environment variables
docker-compose -f docker-compose.cloud.yml exec app env | grep DB

# Test network connectivity
docker-compose -f docker-compose.cloud.yml exec app ping db.[YOUR-PROJECT-REF].supabase.co
```

## 🔄 Migration from Local to Supabase

### 1. Export Local Data (if needed)
```bash
# Export from local PostgreSQL
docker-compose exec postgres pg_dump -U carwash_user mobile_carwash_dev > local_backup.sql
```

### 2. Import to Supabase
```bash
# Import to Supabase (modify connection details)
psql "postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres" < local_backup.sql
```

### 3. Update Configuration
```bash
# Switch to cloud configuration
cp .env .env.local.backup
# Update .env with Supabase URL
# Restart with cloud configuration
npm run docker:cloud
```

## 📈 Performance Optimization

### Connection Pooling
```env
# Optimize for Supabase
DB_POOL_MAX=10
DB_POOL_MIN=2
DB_CONNECTION_TIMEOUT=30000
```

### Query Optimization
- Use Supabase's built-in query performance monitoring
- Enable slow query logging in Supabase dashboard
- Monitor connection usage in Supabase metrics

## 🔐 Security Best Practices

1. **Environment Variables**: Never commit `.env` files
2. **Database Password**: Use strong, unique passwords
3. **SSL**: Always use SSL in production
4. **Connection Limits**: Set appropriate pool limits
5. **Monitoring**: Enable Supabase audit logs

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Database Guide](https://supabase.com/docs/guides/database)
- [PostgreSQL Connection Pooling](https://supabase.com/docs/guides/database/connecting-to-postgres#connection-pooler)
- [Supabase Performance Tips](https://supabase.com/docs/guides/database/database-performance)

import express from "express";
import authRoutes from "./authRoutes";
import userRoutes from "./userRoutes";
import serviceRoutes from "./serviceRoutes";
import bookingRoutes from "./bookingRoutes";
import roleRoutes from "./roleRoutes";
import teamRoutes from "./teamRoutes";
import stripeRoutes from "./stripeRoutes";
import sequelize from "../config/database";

const router = express.Router();

// Health check endpoint for Docker and monitoring
router.get("/health", async (req, res) => {
    try {
        // Check database connection
        await sequelize.authenticate();

        const healthStatus = {
            status: "healthy",
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || "development",
            version: process.env.npm_package_version || "1.0.0",
            database: "connected",
            memory: {
                used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
            },
        };

        res.status(200).json(healthStatus);
    } catch (error) {
        const healthStatus = {
            status: "unhealthy",
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || "development",
            version: process.env.npm_package_version || "1.0.0",
            database: "disconnected",
            error: error instanceof Error ? error.message : "Unknown error",
        };

        res.status(503).json(healthStatus);
    }
});

router.use("/auth", authRoutes);
router.use("/users", userRoutes);
router.use("/services", serviceRoutes);
router.use("/bookings", bookingRoutes);
router.use("/roles", roleRoutes);
router.use("/teams", teamRoutes);
router.use("/stripe", stripeRoutes);

export default router;

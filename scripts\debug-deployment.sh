#!/bin/bash

# Debug Deployment Script
# Run this on your server to check why code changes aren't reflected

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if running on server
if [ ! -d "/root/mobile_carwash_server" ]; then
    print_error "This script should be run on your DigitalOcean server"
    print_status "Usage: ssh <EMAIL> 'bash -s' < scripts/debug-deployment.sh"
    exit 1
fi

cd /root/mobile_carwash_server

print_header "Deployment Debug Report"

# 1. Check Docker images
print_status "1. Checking Docker images..."
echo "Available mobile-carwash images:"
sudo docker images mobile-carwash --format "table {{.Tag}}\t{{.CreatedAt}}\t{{.Size}}"
echo ""

# 2. Check running containers
print_status "2. Checking running containers..."
echo "Running containers:"
sudo docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
echo ""

# 3. Check container details
print_status "3. Checking container details..."
if sudo docker ps | grep -q mobile-carwash-api-cloud; then
    CONTAINER_ID=$(sudo docker ps | grep mobile-carwash-api-cloud | awk '{print $1}')
    echo "Container ID: $CONTAINER_ID"
    echo "Container image: $(sudo docker inspect $CONTAINER_ID --format='{{.Config.Image}}')"
    echo "Container created: $(sudo docker inspect $CONTAINER_ID --format='{{.Created}}')"
    echo ""
else
    print_warning "mobile-carwash-api-cloud container not found"
fi

# 4. Check code in container
print_status "4. Checking code in container..."
if sudo docker ps | grep -q mobile-carwash-api-cloud; then
    echo "Files in /app/dist/:"
    sudo docker exec mobile-carwash-api-cloud ls -la /app/dist/ | head -10
    echo ""
    
    echo "Package.json version in container:"
    sudo docker exec mobile-carwash-api-cloud cat /app/package.json | grep '"version"' || echo "Version not found"
    echo ""
    
    echo "Main index.js file (first 10 lines):"
    sudo docker exec mobile-carwash-api-cloud head -10 /app/dist/index.js || echo "File not found"
    echo ""
else
    print_warning "Cannot check code - container not running"
fi

# 5. Check docker-compose file
print_status "5. Checking docker-compose configuration..."
if [ -f "docker-compose.cloud.yml" ]; then
    echo "Docker-compose file exists"
    echo "Image specified in docker-compose:"
    grep -A 5 "image:" docker-compose.cloud.yml || echo "No image specified (using build)"
    echo ""
    
    echo "Build configuration:"
    grep -A 10 "build:" docker-compose.cloud.yml || echo "No build configuration"
    echo ""
else
    print_error "docker-compose.cloud.yml not found"
fi

# 6. Check recent logs
print_status "6. Checking recent application logs..."
if sudo docker ps | grep -q mobile-carwash-api-cloud; then
    echo "Last 20 lines of application logs:"
    sudo docker-compose -f docker-compose.cloud.yml logs --tail=20 app
    echo ""
else
    print_warning "Cannot check logs - container not running"
fi

# 7. Check environment variables
print_status "7. Checking environment variables..."
if sudo docker ps | grep -q mobile-carwash-api-cloud; then
    echo "NODE_ENV: $(sudo docker exec mobile-carwash-api-cloud printenv NODE_ENV || echo 'Not set')"
    echo "PORT: $(sudo docker exec mobile-carwash-api-cloud printenv PORT || echo 'Not set')"
    echo ""
else
    print_warning "Cannot check environment - container not running"
fi

# 8. Check health endpoint
print_status "8. Testing health endpoint..."
if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
    print_success "Health endpoint is responding"
    echo "Response:"
    curl -s http://localhost:8080/api/health | head -5
else
    print_error "Health endpoint is not responding"
fi
echo ""

# 9. Check disk space
print_status "9. Checking disk space..."
echo "Disk usage:"
df -h /
echo ""

echo "Docker system usage:"
sudo docker system df
echo ""

# 10. Recommendations
print_header "Recommendations"

if sudo docker images mobile-carwash | grep -q "latest"; then
    LATEST_CREATED=$(sudo docker images mobile-carwash:latest --format "{{.CreatedAt}}")
    echo "✅ Latest image exists (created: $LATEST_CREATED)"
else
    print_error "❌ No 'latest' image found"
    echo "🔧 Fix: Re-run deployment or manually tag an image as latest"
fi

if sudo docker ps | grep -q mobile-carwash-api-cloud; then
    print_success "✅ Container is running"
else
    print_error "❌ Container is not running"
    echo "🔧 Fix: Run 'sudo docker-compose -f docker-compose.cloud.yml up -d'"
fi

if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
    print_success "✅ Application is responding"
else
    print_error "❌ Application is not responding"
    echo "🔧 Fix: Check logs and restart container"
fi

print_header "Quick Fixes"
echo "If code is not updated, try these commands:"
echo ""
echo "1. Force recreate containers:"
echo "   sudo docker-compose -f docker-compose.cloud.yml down"
echo "   sudo docker-compose -f docker-compose.cloud.yml up -d --force-recreate"
echo ""
echo "2. Remove old images and restart:"
echo "   sudo docker-compose -f docker-compose.cloud.yml down"
echo "   sudo docker rmi mobile-carwash:latest || true"
echo "   # Then re-run your GitHub Actions deployment"
echo ""
echo "3. Check if you're using the right branch:"
echo "   # Make sure you pushed to 'develop' branch for development deployment"
echo ""

print_success "Debug report completed!"

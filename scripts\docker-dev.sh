#!/bin/bash

# Docker Development Management Script
# Usage: ./scripts/docker-dev.sh [start|stop|restart|logs|shell|db|clean]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to start development environment (local database)
start_dev() {
    print_status "Starting development environment with local database..."

    # Check if .env file exists
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_warning "Please edit .env file with your configuration before starting."
        return 1
    fi

    docker-compose up -d
    print_success "Development environment started!"
    print_status "Services:"
    print_status "  - API Server: http://localhost:8080"
    print_status "  - Database: localhost:5432"
    print_status "  - pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
    print_status ""
    print_status "To view logs: $0 logs"
    print_status "To access shell: $0 shell"
}

# Function to start cloud development environment (Supabase/Cloud DB)
start_cloud() {
    print_status "Starting development environment with cloud database..."

    # Check if .env file exists
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_warning "Please configure your cloud database URL in .env before starting."
        return 1
    fi

    # Check if DB_URI contains cloud database
    if grep -q "localhost\|postgres:" .env; then
        print_warning "Warning: .env appears to contain local database configuration."
        print_warning "For cloud setup, update DB_URI with your Supabase/cloud database URL."
    fi

    docker-compose -f docker-compose.cloud.yml up -d
    print_success "Cloud development environment started!"
    print_status "Services:"
    print_status "  - API Server: http://localhost:8080"
    print_status "  - Cloud Database: (configured in .env)"
    print_status "  - Redis: localhost:6379"
    print_status ""
    print_status "To view logs: $0 cloud-logs"
    print_status "To access shell: $0 shell"
}

# Function to start with tools (pgAdmin)
start_with_tools() {
    print_status "Starting development environment with tools..."
    docker-compose --profile tools up -d
    print_success "Development environment with tools started!"
}

# Function to stop development environment
stop_dev() {
    print_status "Stopping development environment..."
    docker-compose down
    print_success "Development environment stopped!"
}

# Function to restart development environment
restart_dev() {
    print_status "Restarting development environment..."
    docker-compose restart
    print_success "Development environment restarted!"
}

# Function to view logs
view_logs() {
    print_status "Viewing application logs (Ctrl+C to exit)..."
    docker-compose logs -f app
}

# Function to view cloud logs
view_cloud_logs() {
    print_status "Viewing cloud application logs (Ctrl+C to exit)..."
    docker-compose -f docker-compose.cloud.yml logs -f app
}

# Function to stop cloud environment
stop_cloud() {
    print_status "Stopping cloud development environment..."
    docker-compose -f docker-compose.cloud.yml down
    print_success "Cloud development environment stopped!"
}

# Function to access application shell
access_shell() {
    print_status "Accessing application container shell..."
    docker-compose exec app sh
}

# Function to access database shell
access_db() {
    print_status "Accessing PostgreSQL shell..."
    docker-compose exec postgres psql -U carwash_user -d mobile_carwash_dev
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    docker-compose exec app npm run migrate
    print_success "Database migrations completed!"
}

# Function to seed database
seed_db() {
    print_status "Seeding database..."
    docker-compose exec app npm run seed
    print_success "Database seeded!"
}

# Function to backup database
backup_db() {
    print_status "Creating database backup..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    backup_file="backup_${timestamp}.sql"
    docker-compose exec postgres pg_dump -U carwash_user mobile_carwash_dev > "backups/${backup_file}"
    print_success "Database backup created: backups/${backup_file}"
}

# Function to clean up Docker resources
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose down -v
    docker system prune -f
    docker volume prune -f
    print_success "Cleanup completed!"
}

# Function to show status
show_status() {
    print_status "Docker Compose Status:"
    docker-compose ps
    print_status ""
    print_status "Docker Images:"
    docker images | grep mobile-carwash || echo "No mobile-carwash images found"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Database Setup Commands:"
    echo "  start       Start development environment (local database)"
    echo "  cloud       Start development environment (cloud database/Supabase)"
    echo "  tools       Start with development tools (pgAdmin)"
    echo ""
    echo "Management Commands:"
    echo "  stop        Stop development environment"
    echo "  cloud-stop  Stop cloud development environment"
    echo "  restart     Restart development environment"
    echo "  logs        View application logs (local setup)"
    echo "  cloud-logs  View application logs (cloud setup)"
    echo "  shell       Access application container shell"
    echo "  db          Access PostgreSQL shell (local only)"
    echo ""
    echo "Database Commands:"
    echo "  migrate     Run database migrations"
    echo "  seed        Seed database with initial data"
    echo "  backup      Create database backup (local only)"
    echo ""
    echo "Utility Commands:"
    echo "  status      Show Docker status"
    echo "  clean       Clean up Docker resources"
    echo ""
    echo "Examples:"
    echo "  $0 start          # Local database development"
    echo "  $0 cloud          # Cloud database development (Supabase)"
    echo "  $0 cloud-logs     # View logs for cloud setup"
    echo "  $0 shell          # Access container shell"
}

# Create backups directory if it doesn't exist
mkdir -p backups

# Main script logic
case "${1:-start}" in
    "start")
        start_dev
        ;;
    "cloud")
        start_cloud
        ;;
    "tools")
        start_with_tools
        ;;
    "stop")
        stop_dev
        ;;
    "cloud-stop")
        stop_cloud
        ;;
    "restart")
        restart_dev
        ;;
    "logs")
        view_logs
        ;;
    "cloud-logs")
        view_cloud_logs
        ;;
    "shell")
        access_shell
        ;;
    "db")
        access_db
        ;;
    "migrate")
        run_migrations
        ;;
    "seed")
        seed_db
        ;;
    "backup")
        backup_db
        ;;
    "status")
        show_status
        ;;
    "clean")
        cleanup
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Invalid command: $1"
        show_usage
        exit 1
        ;;
esac

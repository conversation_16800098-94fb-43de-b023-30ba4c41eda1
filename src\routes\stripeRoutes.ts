import express from "express";
import * as stripeController from "../controllers/stripeController";
import { authenticate } from "../middleware/auth";

const router = express.Router();

// Get current user's Stripe customer information (users can view their own)
router.get("/customer", authenticate, stripeController.getCustomerInfo);

// Create Stripe customer for current user (users can create their own)
router.post("/customer", authenticate, stripeController.createCustomer);

// Get payment methods for current user (users can view their own payment methods)
router.get("/payment-methods", authenticate, stripeController.getPaymentMethods);

// Get Stripe publishable key (all authenticated users can access)
router.get("/config", authenticate, stripeController.getPublishableKey);

// Capture payment for completed booking (admin/manager or booking owner)
router.post("/capture/:bookingId", authenticate, stripeController.capturePayment);

// Cancel payment for cancelled booking (admin/manager or booking owner)
router.post("/cancel/:bookingId", authenticate, stripeController.cancelPayment);

export default router;

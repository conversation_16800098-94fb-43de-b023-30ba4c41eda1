/**
 * Example usage of the Enhanced Booking system with team assignments
 * This file demonstrates how to use the new booking functionality
 */

import { Booking, Service, Team, User, BookingService } from "../models";

export class EnhancedBookingExamples {
    // Example: Create a booking with automatic team assignment
    static async createBookingWithTeamAssignment() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(10, 0, 0, 0); // 10 AM tomorrow

        // Get a service
        const service = await Service.findOne({
            where: { name: "Basic Wash" },
        });
        if (!service) {
            throw new Error("Service not found");
        }

        // Calculate end time based on service duration
        const startTime = tomorrow;
        const endTime = Booking.calculateEndTime(startTime, service.duration);

        // Check for available teams
        const availableTeams = await Booking.findAvailableTeams(
            startTime,
            endTime
        );
        console.log(`Available teams: ${availableTeams.length}`);

        if (availableTeams.length === 0) {
            console.log("No teams available for this time slot");
            return null;
        }

        // Create booking with first available team
        const booking = await Booking.createRecord({
            userId: 1, // Example user ID
            teamId: availableTeams[0],
            bookingDate: new Date(startTime.toISOString().split("T")[0]),
            startTime,
            endTime,
            status: "pending",
            address: "123 Example Street, City, State 12345",
            notes: "Automatic team assignment example",
        });

        // Add service to booking through BookingService junction table
        await BookingService.createRecord({
            bookingId: booking.id,
            serviceId: service.id,
            isMainService: true
        });

        console.log("Booking created:", booking.toJSON());
        return booking;
    }

    // Example: Check team availability for a specific date
    static async checkTeamAvailabilityForDate(date: Date) {
        const teams = await Team.getActiveTeams();

        for (const team of teams) {
            const schedule = await Booking.getTeamSchedule(team.id, date);
            console.log(`\nTeam: ${team.name}`);
            console.log(`Bookings for ${date.toISOString().split("T")[0]}:`);

            if (schedule.length === 0) {
                console.log("  No bookings - fully available");
            } else {
                schedule.forEach((booking) => {
                    console.log(
                        `  ${booking.startTime.toLocaleTimeString()} - ${booking.endTime.toLocaleTimeString()}: ${
                            booking.status
                        }`
                    );
                });
            }
        }
    }

    // Example: Find next available time slot
    static async findNextAvailableSlot(
        preferredTime: Date,
        serviceDuration: number
    ) {
        const startTime = new Date(preferredTime);

        // Try every hour for the next 8 hours
        for (let i = 0; i < 8; i++) {
            const testStartTime = new Date(startTime);
            testStartTime.setHours(startTime.getHours() + i);

            // Skip if outside business hours (8 AM - 6 PM)
            if (testStartTime.getHours() < 8 || testStartTime.getHours() > 17) {
                continue;
            }

            const testEndTime = Booking.calculateEndTime(
                testStartTime,
                serviceDuration
            );
            const availableTeams = await Booking.findAvailableTeams(
                testStartTime,
                testEndTime
            );

            if (availableTeams.length > 0) {
                console.log(
                    `Next available slot: ${testStartTime.toISOString()}`
                );
                console.log(`Available teams: ${availableTeams.length}`);
                return {
                    startTime: testStartTime,
                    endTime: testEndTime,
                    availableTeams,
                };
            }
        }

        console.log("No available slots found in the next 8 hours");
        return null;
    }

    // Example: Reschedule a booking
    static async rescheduleBooking(bookingId: number, newStartTime: Date) {
        const booking = await Booking.findById(bookingId, {
            include: [
                {
                    model: Service,
                    as: "services",
                    through: { attributes: ["isMainService"] }
                }
            ],
        });

        if (!booking) {
            throw new Error("Booking not found");
        }

        // Get the main service to calculate duration
        const mainService = booking.Services?.find((service: any) =>
            service.BookingService?.isMainService === true
        );

        if (!mainService) {
            throw new Error("No main service found for booking");
        }

        const newEndTime = Booking.calculateEndTime(
            newStartTime,
            mainService.duration
        );

        // Check if rescheduling is possible
        const canReschedule = await booking.canReschedule(
            newStartTime,
            newEndTime
        );

        if (!canReschedule) {
            console.log(
                "Cannot reschedule to the requested time - team not available"
            );

            // Find alternative times
            const alternatives = await this.findNextAvailableSlot(
                newStartTime,
                mainService.duration
            );
            if (alternatives) {
                console.log(
                    "Suggested alternative:",
                    alternatives.startTime.toISOString()
                );
            }
            return false;
        }

        // Update the booking
        await booking.update({
            startTime: newStartTime,
            endTime: newEndTime,
            bookingDate: new Date(newStartTime.toISOString().split("T")[0]),
        });

        console.log("Booking rescheduled successfully");
        return true;
    }

    // Example: Get team utilization report
    static async getTeamUtilizationReport(startDate: Date, endDate: Date) {
        const teams = await Team.getActiveTeams();
        const { Op } = require("sequelize");

        const report = await Promise.all(
            teams.map(async (team) => {
                const bookings = await Booking.findAll({
                    where: {
                        teamId: team.id,
                        bookingDate: {
                            [Op.between]: [startDate, endDate],
                        },
                        status: {
                            [Op.in]: ["confirmed", "completed"],
                        },
                    },
                    include: [
                        {
                            model: Service,
                            as: "services",
                            through: { attributes: ["isMainService"] }
                        }
                    ],
                });

                const totalMinutes = bookings.reduce((sum, booking) => {
                    return sum + booking.getDurationMinutes();
                }, 0);

                const totalHours = Math.round((totalMinutes / 60) * 100) / 100;

                return {
                    teamId: team.id,
                    teamName: team.name,
                    totalBookings: bookings.length,
                    totalHours,
                    averageBookingDuration:
                        bookings.length > 0
                            ? Math.round(totalMinutes / bookings.length)
                            : 0,
                };
            })
        );

        console.log("Team Utilization Report:");
        console.table(report);
        return report;
    }

    // Example: Check for booking conflicts
    static async checkBookingConflicts(
        teamId: number,
        startTime: Date,
        endTime: Date
    ) {
        const isAvailable = await Booking.isTeamAvailable(
            teamId,
            startTime,
            endTime
        );

        if (!isAvailable) {
            const { Op } = require("sequelize");

            // Find conflicting bookings
            const conflicts = await Booking.findAll({
                where: {
                    teamId,
                    status: {
                        [Op.in]: ["confirmed", "in_progress"],
                    },
                    [Op.or]: [
                        {
                            startTime: { [Op.lte]: startTime },
                            endTime: { [Op.gt]: startTime },
                        },
                        {
                            startTime: { [Op.lt]: endTime },
                            endTime: { [Op.gte]: endTime },
                        },
                        {
                            startTime: { [Op.gte]: startTime },
                            endTime: { [Op.lte]: endTime },
                        },
                    ],
                },
                include: [
                    { model: User, attributes: ["firstName", "lastName"] },
                    {
                        model: Service,
                        as: "services",
                        attributes: ["name"],
                        through: { attributes: ["isMainService"] }
                    },
                ],
            });

            console.log("Booking conflicts found:");
            conflicts.forEach((conflict) => {
                console.log(
                    `- ${conflict.startTime.toISOString()} to ${conflict.endTime.toISOString()}`
                );
                console.log(
                    `  Customer: ${conflict.User?.firstName} ${conflict.User?.lastName}`
                );
                const mainService = conflict.Services?.find((service: any) =>
                    service.BookingService?.isMainService === true
                );
                console.log(`  Service: ${mainService?.name || 'Unknown'}`);
            });

            return conflicts;
        }

        console.log("No conflicts - time slot is available");
        return [];
    }

    // Example: Get booking statistics
    static async getBookingStatistics(startDate?: Date, endDate?: Date) {
        const { Op } = require("sequelize");

        let dateFilter = {};
        if (startDate && endDate) {
            dateFilter = {
                bookingDate: {
                    [Op.between]: [startDate, endDate],
                },
            };
        }

        const stats = {
            total: await Booking.countRecords({ where: dateFilter }),
            pending: await Booking.countRecords({
                where: { ...dateFilter, status: "pending" },
            }),
            confirmed: await Booking.countRecords({
                where: { ...dateFilter, status: "confirmed" },
            }),
            inProgress: await Booking.countRecords({
                where: { ...dateFilter, status: "in_progress" },
            }),
            completed: await Booking.countRecords({
                where: { ...dateFilter, status: "completed" },
            }),
            cancelled: await Booking.countRecords({
                where: { ...dateFilter, status: "cancelled" },
            }),
        };

        // Calculate rates
        const completionRate =
            stats.total > 0
                ? ((stats.completed / stats.total) * 100).toFixed(2)
                : "0";
        const cancellationRate =
            stats.total > 0
                ? ((stats.cancelled / stats.total) * 100).toFixed(2)
                : "0";

        console.log("Booking Statistics:");
        console.log(`Total Bookings: ${stats.total}`);
        console.log(`Pending: ${stats.pending}`);
        console.log(`Confirmed: ${stats.confirmed}`);
        console.log(`In Progress: ${stats.inProgress}`);
        console.log(`Completed: ${stats.completed}`);
        console.log(`Cancelled: ${stats.cancelled}`);
        console.log(`Completion Rate: ${completionRate}%`);
        console.log(`Cancellation Rate: ${cancellationRate}%`);

        return { ...stats, completionRate, cancellationRate };
    }

    // Example: Simulate a day's booking workflow
    static async simulateDayWorkflow() {
        console.log("🚀 Simulating a day's booking workflow...");

        const today = new Date();
        today.setHours(9, 0, 0, 0); // Start at 9 AM

        // Try to create bookings every 2 hours
        for (let hour = 9; hour <= 17; hour += 2) {
            const bookingTime = new Date(today);
            bookingTime.setHours(hour);

            console.log(
                `\n⏰ Trying to book at ${bookingTime.toLocaleTimeString()}`
            );

            const service = await Service.findOne({
                where: { name: "Basic Wash" },
            });
            if (!service) continue;

            const endTime = Booking.calculateEndTime(
                bookingTime,
                service.duration
            );
            const availableTeams = await Booking.findAvailableTeams(
                bookingTime,
                endTime
            );

            if (availableTeams.length > 0) {
                console.log(
                    `✅ Team ${availableTeams[0]} available - booking created`
                );

                const booking = await Booking.createRecord({
                    userId: 1,
                    teamId: availableTeams[0],
                    bookingDate: new Date(
                        bookingTime.toISOString().split("T")[0]
                    ),
                    startTime: bookingTime,
                    endTime,
                    status: "confirmed",
                    address: `${hour} Example Street`,
                    notes: `Simulated booking for ${hour}:00`,
                });

                // Add service to booking through BookingService junction table
                await BookingService.createRecord({
                    bookingId: booking.id,
                    serviceId: service.id,
                    isMainService: true
                });
            } else {
                console.log(
                    `❌ No teams available at ${bookingTime.toLocaleTimeString()}`
                );
            }
        }

        console.log("\n📊 Final day statistics:");
        await this.getBookingStatistics(today, today);
    }
}

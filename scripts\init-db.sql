-- Database initialization script for Mobile Carwash Server
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create additional database for testing if needed
-- CREATE DATABASE mobile_carwash_test;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE mobile_carwash_dev TO carwash_user;

-- Create schemas if needed
-- CREATE SCHEMA IF NOT EXISTS public;

-- Set timezone
SET timezone = 'UTC';

-- Log the initialization
DO $$
BEGIN
    RAISE NOTICE 'Mobile Carwash Database initialized successfully at %', NOW();
END $$;

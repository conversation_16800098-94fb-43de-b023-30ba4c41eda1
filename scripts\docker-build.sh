#!/bin/bash

# Docker Build Script for Mobile Carwash Server
# Usage: ./scripts/docker-build.sh [dev|prod|all]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build development image
build_dev() {
    print_status "Building development image..."
    docker build --target development -t mobile-carwash:dev .
    print_success "Development image built successfully!"
}

# Function to build production image
build_prod() {
    print_status "Building production image..."
    docker build --target production -t mobile-carwash:prod .
    print_success "Production image built successfully!"
}

# Function to clean up old images
cleanup() {
    print_status "Cleaning up old images..."
    docker image prune -f
    print_success "Cleanup completed!"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [dev|prod|all|clean]"
    echo ""
    echo "Commands:"
    echo "  dev     Build development image"
    echo "  prod    Build production image"
    echo "  all     Build both development and production images"
    echo "  clean   Clean up old Docker images"
    echo ""
    echo "Examples:"
    echo "  $0 dev"
    echo "  $0 prod"
    echo "  $0 all"
}

# Main script logic
case "${1:-all}" in
    "dev")
        build_dev
        ;;
    "prod")
        build_prod
        ;;
    "all")
        build_dev
        build_prod
        ;;
    "clean")
        cleanup
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Invalid command: $1"
        show_usage
        exit 1
        ;;
esac

print_success "Docker build script completed!"

import { Request, Response } from "express";
import { User, Booking } from "../models";
import stripeService from "../services/stripeService";

/**
 * Get current user's Stripe customer information
 */
export const getCustomerInfo = async (
    req: Request,
    res: Response
): Promise<void> => {
    try {
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "User not authenticated" });
            return;
        }

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ error: "User not found" });
            return;
        }

        if (!user.stripeCustomerId) {
            res.status(404).json({
                error: "Stripe customer not found",
                message: "User does not have a Stripe customer account",
            });
            return;
        }

        const stripeCustomer = await user.getStripeCustomer();

        res.json({
            success: true,
            customer: {
                id: stripeCustomer.id,
                email: stripeCustomer.email,
                name: stripeCustomer.name,
                phone: stripeCustomer.phone,
                created: stripeCustomer.created,
                metadata: stripeCustomer.metadata,
            },
        });
    } catch (error) {
        console.error("Error getting customer info:", error);
        res.status(500).json({
            error: "Failed to get customer information",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};

/**
 * Create Stripe customer for current user if not exists
 */
export const createCustomer = async (
    req: Request,
    res: Response
): Promise<void> => {
    try {
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "User not authenticated" });
            return;
        }

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ error: "User not found" });
            return;
        }

        if (user.stripeCustomerId) {
            res.status(400).json({
                error: "Customer already exists",
                customerId: user.stripeCustomerId,
            });
            return;
        }

        const stripeCustomerId = await user.ensureStripeCustomer();

        res.json({
            success: true,
            message: "Stripe customer created successfully",
            customerId: stripeCustomerId,
        });
    } catch (error) {
        console.error("Error creating customer:", error);
        res.status(500).json({
            error: "Failed to create customer",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};

/**
 * Get payment methods for current user
 */
export const getPaymentMethods = async (
    req: Request,
    res: Response
): Promise<void> => {
    try {
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "User not authenticated" });
            return;
        }

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ error: "User not found" });
            return;
        }

        if (!user.stripeCustomerId) {
            res.status(404).json({
                error: "Stripe customer not found",
                message: "User does not have a Stripe customer account",
            });
            return;
        }

        const paymentMethods = await user.getPaymentMethods();

        res.json({
            success: true,
            paymentMethods: paymentMethods.map((pm) => ({
                id: pm.id,
                type: pm.type,
                card: pm.card
                    ? {
                          brand: pm.card.brand,
                          last4: pm.card.last4,
                          exp_month: pm.card.exp_month,
                          exp_year: pm.card.exp_year,
                      }
                    : null,
                created: pm.created,
            })),
        });
    } catch (error) {
        console.error("Error getting payment methods:", error);
        res.status(500).json({
            error: "Failed to get payment methods",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};



/**
 * Get Stripe publishable key for frontend
 */
export const getPublishableKey = async (
    _req: Request,
    res: Response
): Promise<void> => {
    try {
        const publishableKey = process.env.STRIPE_PUBLISHABLE_KEY;

        if (!publishableKey) {
            res.status(500).json({
                error: "Stripe publishable key not configured",
            });
            return;
        }

        res.json({
            success: true,
            publishableKey,
        });
    } catch (error) {
        console.error("Error getting publishable key:", error);
        res.status(500).json({
            error: "Failed to get publishable key",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};

/**
 * Capture payment for a completed booking
 */
export const capturePayment = async (req: Request, res: Response): Promise<void> => {
    try {
        const { bookingId } = req.params;
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: 'User not authenticated' });
            return;
        }

        if (!bookingId) {
            res.status(400).json({ error: 'Booking ID is required' });
            return;
        }

        // Find the booking
        const booking = await Booking.findById(parseInt(bookingId));
        if (!booking) {
            res.status(404).json({ error: 'Booking not found' });
            return;
        }

        // Check authorization (admin/manager or booking owner)
        const canCapture = req.user?.roles.includes('admin') ||
                          req.user?.roles.includes('manager') ||
                          booking.userId === userId;

        if (!canCapture) {
            res.status(403).json({ error: 'Not authorized to capture payment for this booking' });
            return;
        }

        // Only capture payment for completed bookings
        if (booking.status !== 'completed') {
            res.status(400).json({
                error: 'Can only capture payment for completed bookings',
                currentStatus: booking.status
            });
            return;
        }

        // Check if booking has a payment intent ID
        if (!booking.paymentIntentId) {
            res.status(400).json({
                error: 'No payment intent found for this booking',
                message: 'This booking may not have been created with payment authorization'
            });
            return;
        }

        try {
            // Capture the payment
            const capturedPayment = await stripeService.capturePaymentIntent(booking.paymentIntentId);

            res.json({
                success: true,
                message: 'Payment captured successfully',
                paymentIntent: {
                    id: capturedPayment.id,
                    status: capturedPayment.status,
                    amount: capturedPayment.amount,
                    currency: capturedPayment.currency
                }
            });

        } catch (error) {
            console.error('Error capturing payment:', error);
            res.status(500).json({
                error: 'Failed to capture payment',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }

    } catch (error) {
        console.error('Error capturing payment:', error);
        res.status(500).json({
            error: 'Failed to capture payment',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

/**
 * Cancel payment for a cancelled booking
 */
export const cancelPayment = async (req: Request, res: Response): Promise<void> => {
    try {
        const { bookingId } = req.params;
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: 'User not authenticated' });
            return;
        }

        if (!bookingId) {
            res.status(400).json({ error: 'Booking ID is required' });
            return;
        }

        // Find the booking
        const booking = await Booking.findById(parseInt(bookingId));
        if (!booking) {
            res.status(404).json({ error: 'Booking not found' });
            return;
        }

        // Check authorization (admin/manager or booking owner)
        const canCancel = req.user?.roles.includes('admin') ||
                         req.user?.roles.includes('manager') ||
                         booking.userId === userId;

        if (!canCancel) {
            res.status(403).json({ error: 'Not authorized to cancel payment for this booking' });
            return;
        }

        // Only cancel payment for cancelled bookings
        if (booking.status !== 'cancelled') {
            res.status(400).json({
                error: 'Can only cancel payment for cancelled bookings',
                currentStatus: booking.status
            });
            return;
        }

        // Check if booking has a payment intent ID
        if (!booking.paymentIntentId) {
            res.status(400).json({
                error: 'No payment intent found for this booking',
                message: 'This booking may not have been created with payment authorization'
            });
            return;
        }

        try {
            // Cancel the payment
            const cancelledPayment = await stripeService.cancelPaymentIntent(booking.paymentIntentId);

            res.json({
                success: true,
                message: 'Payment cancelled successfully',
                paymentIntent: {
                    id: cancelledPayment.id,
                    status: cancelledPayment.status,
                    amount: cancelledPayment.amount,
                    currency: cancelledPayment.currency
                }
            });

        } catch (error) {
            console.error('Error cancelling payment:', error);
            res.status(500).json({
                error: 'Failed to cancel payment',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }

    } catch (error) {
        console.error('Error cancelling payment:', error);
        res.status(500).json({
            error: 'Failed to cancel payment',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};


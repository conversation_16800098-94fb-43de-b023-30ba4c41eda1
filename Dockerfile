# Use Node.js 18 Alpine as the base image for smaller size and better security
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies required for native modules (bcrypt, pg-native, etc.)
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    postgresql-client \
    && ln -sf python3 /usr/bin/python

# Copy package files for dependency installation
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Development stage
FROM base AS development

# Install all dependencies including devDependencies for development
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Expose port
EXPOSE 8080

# Set environment to development
ENV NODE_ENV=development

# Command for development (with hot reload)
CMD ["npm", "run", "dev"]

# Build stage
FROM base AS build

# Install all dependencies including devDependencies for building
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Build TypeScript code
RUN npm run build

# Production stage
FROM base AS production

# Copy built application from build stage
COPY --from=build /app/dist ./dist

# Copy package files (already copied in base stage)
# COPY package*.json ./

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory to nodejs user
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 8080

# Set environment to production
ENV NODE_ENV=production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/health || exit 1

# Command for production
CMD ["npm", "start"]

# Multi-stage build target selection
# Default target is production
# Use --target development for dev builds
# Use --target production for prod builds (default)

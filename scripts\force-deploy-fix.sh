#!/bin/bash

# Force Deploy Fix Script
# Run this on your server to completely reset and force new deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on server
if [ ! -d "/root/mobile_carwash_server" ]; then
    print_error "This script should be run on your DigitalOcean server"
    print_status "Usage: ssh <EMAIL> 'bash -s' < scripts/force-deploy-fix.sh"
    exit 1
fi

cd /root/mobile_carwash_server

print_status "🔄 Starting force deployment reset..."

# 1. Stop all services
print_status "1. Stopping all services..."
sudo docker-compose -f docker-compose.cloud.yml down --volumes --remove-orphans || true
sudo docker-compose -f docker-compose.deploy.yml down --volumes --remove-orphans || true
sudo docker-compose down --volumes --remove-orphans || true

# 2. Remove all mobile-carwash containers
print_status "2. Removing all mobile-carwash containers..."
sudo docker ps -a | grep mobile-carwash | awk '{print $1}' | xargs -r sudo docker rm -f || true

# 3. Remove all mobile-carwash images
print_status "3. Removing all mobile-carwash images..."
sudo docker images mobile-carwash -q | xargs -r sudo docker rmi -f || true

# 4. Clean up Docker system
print_status "4. Cleaning up Docker system..."
sudo docker system prune -f
sudo docker volume prune -f

# 5. Show current state
print_status "5. Current Docker state:"
echo "Images:"
sudo docker images | grep mobile-carwash || echo "No mobile-carwash images found"
echo ""
echo "Containers:"
sudo docker ps -a | grep mobile-carwash || echo "No mobile-carwash containers found"
echo ""

# 6. Create deployment compose file if it doesn't exist
if [ ! -f "docker-compose.deploy.yml" ]; then
    print_status "6. Creating deployment compose file..."
    cat > docker-compose.deploy.yml << 'EOF'
version: "3.8"

services:
  app:
    image: mobile-carwash:latest
    container_name: mobile-carwash-api-cloud
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=8080
      - DB_URI=${DB_URI}
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-for-development}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: mobile-carwash-redis-cloud
    ports:
      - "6379:6379"
    volumes:
      - redis_cloud_data:/data
    networks:
      - carwash-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

volumes:
  redis_cloud_data:
    driver: local

networks:
  carwash-network:
    driver: bridge
    name: mobile-carwash-network-cloud
EOF
    print_success "Deployment compose file created"
else
    print_status "6. Deployment compose file already exists"
fi

print_success "✅ Force reset completed!"
print_warning "⚠️ Now re-run your GitHub Actions deployment to get fresh code"

print_status "Next steps:"
echo "1. Go to GitHub → Actions"
echo "2. Select 'Deploy to Development'"
echo "3. Click 'Run workflow' on develop branch"
echo "4. Or push a new commit to develop branch"

print_status "To verify after deployment:"
echo "curl http://localhost:8080/api/health"
echo "sudo docker images mobile-carwash"
echo "sudo docker-compose -f docker-compose.deploy.yml ps"

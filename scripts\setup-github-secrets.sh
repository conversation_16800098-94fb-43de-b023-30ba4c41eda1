#!/bin/bash

# GitHub Secrets Setup Helper Script
# This script helps generate the values needed for GitHub Secrets

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to generate JWT secret
generate_jwt_secret() {
    openssl rand -base64 64 | tr -d '\n'
}

# Function to generate SSH key pair
generate_ssh_keys() {
    local key_name="$1"
    local email="$2"
    
    if [ ! -f ~/.ssh/${key_name} ]; then
        print_status "Generating SSH key pair: ${key_name}"
        ssh-keygen -t rsa -b 4096 -C "${email}" -f ~/.ssh/${key_name} -N ""
        print_success "SSH key pair generated: ~/.ssh/${key_name}"
    else
        print_warning "SSH key already exists: ~/.ssh/${key_name}"
    fi
}

# Function to display secret template
display_secret_template() {
    local env_name="$1"
    local server_ip="$2"
    local ssh_key_file="$3"
    local db_uri="$4"
    local stripe_key="$5"
    
    print_header "${env_name} Environment Secrets"
    
    echo "${env_name}_SERVER_IP=${server_ip}"
    echo "${env_name}_SERVER_USER=root"
    echo ""
    echo "${env_name}_SSH_PRIVATE_KEY="
    if [ -f "$ssh_key_file" ]; then
        cat "$ssh_key_file"
    else
        echo "[SSH private key content - generate using this script]"
    fi
    echo ""
    echo "${env_name}_DB_URI=${db_uri}"
    echo "${env_name}_JWT_SECRET=$(generate_jwt_secret)"
    echo "${env_name}_STRIPE_SECRET_KEY=${stripe_key}"
    echo ""
}

# Main setup function
main() {
    print_header "GitHub Actions Secrets Setup Helper"
    
    echo "This script will help you generate the values needed for GitHub Secrets."
    echo "You'll need to manually add these to your GitHub repository settings."
    echo ""
    
    # Get user inputs
    read -p "Enter your email for SSH key generation: " email
    read -p "Enter development server IP: " dev_ip
    read -p "Enter staging server IP: " staging_ip
    read -p "Enter development database URI: " dev_db_uri
    read -p "Enter staging database URI: " staging_db_uri
    read -p "Enter development Stripe secret key: " dev_stripe_key
    read -p "Enter staging Stripe secret key: " staging_stripe_key
    
    echo ""
    print_status "Generating SSH keys..."
    
    # Generate SSH keys
    generate_ssh_keys "github_actions_dev" "$email"
    generate_ssh_keys "github_actions_staging" "$email"
    
    echo ""
    print_success "SSH keys generated successfully!"
    
    # Display public keys for server setup
    print_header "Public Keys for Server Setup"
    echo "Copy these public keys to your servers' ~/.ssh/authorized_keys file:"
    echo ""
    
    echo "Development server public key:"
    if [ -f ~/.ssh/github_actions_dev.pub ]; then
        cat ~/.ssh/github_actions_dev.pub
    fi
    echo ""
    
    echo "Staging server public key:"
    if [ -f ~/.ssh/github_actions_staging.pub ]; then
        cat ~/.ssh/github_actions_staging.pub
    fi
    echo ""
    
    # Display GitHub Secrets
    print_header "GitHub Secrets Configuration"
    echo "Add these secrets to your GitHub repository:"
    echo "Repository → Settings → Secrets and variables → Actions → New repository secret"
    echo ""
    
    # Development secrets
    display_secret_template "DEV" "$dev_ip" ~/.ssh/github_actions_dev "$dev_db_uri" "$dev_stripe_key"
    
    # Staging secrets
    display_secret_template "STAGING" "$staging_ip" ~/.ssh/github_actions_staging "$staging_db_uri" "$staging_stripe_key"
    
    # Server setup commands
    print_header "Server Setup Commands"
    echo "Run these commands on your servers:"
    echo ""
    
    echo "# Development server setup:"
    echo "ssh root@${dev_ip}"
    echo "echo '$(cat ~/.ssh/github_actions_dev.pub 2>/dev/null || echo '[DEV_PUBLIC_KEY]')' >> ~/.ssh/authorized_keys"
    echo "chmod 600 ~/.ssh/authorized_keys"
    echo ""
    
    echo "# Staging server setup:"
    echo "ssh root@${staging_ip}"
    echo "echo '$(cat ~/.ssh/github_actions_staging.pub 2>/dev/null || echo '[STAGING_PUBLIC_KEY]')' >> ~/.ssh/authorized_keys"
    echo "chmod 600 ~/.ssh/authorized_keys"
    echo ""
    
    # Additional setup instructions
    print_header "Next Steps"
    echo "1. Copy the public keys to your servers (commands above)"
    echo "2. Add all the secrets to your GitHub repository"
    echo "3. Test SSH connectivity:"
    echo "   ssh -i ~/.ssh/github_actions_dev root@${dev_ip}"
    echo "   ssh -i ~/.ssh/github_actions_staging root@${staging_ip}"
    echo "4. Set up your servers with Docker and required tools"
    echo "5. Push code to trigger your first deployment!"
    echo ""
    
    print_success "Setup helper completed!"
    print_warning "Remember to keep your private keys secure and never commit them to version control!"
}

# Function to test SSH connectivity
test_ssh_connectivity() {
    print_header "Testing SSH Connectivity"
    
    read -p "Enter server IP to test: " test_ip
    read -p "Enter SSH key file path (e.g., ~/.ssh/github_actions_dev): " key_file
    
    if [ -f "$key_file" ]; then
        print_status "Testing SSH connection to $test_ip..."
        if ssh -i "$key_file" -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@"$test_ip" "echo 'SSH connection successful!'" 2>/dev/null; then
            print_success "SSH connection to $test_ip successful!"
        else
            print_error "SSH connection to $test_ip failed!"
            echo "Make sure:"
            echo "1. The server is running and accessible"
            echo "2. The public key is added to ~/.ssh/authorized_keys on the server"
            echo "3. SSH service is running on the server"
            echo "4. Firewall allows SSH connections"
        fi
    else
        print_error "SSH key file not found: $key_file"
    fi
}

# Function to validate GitHub secrets format
validate_secrets() {
    print_header "GitHub Secrets Validation"
    
    echo "This will help you validate your GitHub Secrets format."
    echo ""
    
    read -p "Enter the path to your SSH private key: " key_file
    
    if [ -f "$key_file" ]; then
        print_status "Validating SSH key format..."
        
        if head -1 "$key_file" | grep -q "BEGIN OPENSSH PRIVATE KEY"; then
            print_success "SSH key format is correct for GitHub Secrets"
            echo ""
            echo "Copy this entire content (including headers) to your GitHub Secret:"
            echo "----------------------------------------"
            cat "$key_file"
            echo "----------------------------------------"
        else
            print_warning "SSH key might not be in the correct format"
            echo "GitHub Actions expects OpenSSH format starting with:"
            echo "-----BEGIN OPENSSH PRIVATE KEY-----"
            echo ""
            echo "Your key starts with:"
            head -1 "$key_file"
        fi
    else
        print_error "SSH key file not found: $key_file"
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  setup     Generate SSH keys and display GitHub Secrets template"
    echo "  test      Test SSH connectivity to a server"
    echo "  validate  Validate SSH key format for GitHub Secrets"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 test"
    echo "  $0 validate"
}

# Main script logic
case "${1:-setup}" in
    "setup")
        main
        ;;
    "test")
        test_ssh_connectivity
        ;;
    "validate")
        validate_secrets
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Invalid command: $1"
        show_usage
        exit 1
        ;;
esac

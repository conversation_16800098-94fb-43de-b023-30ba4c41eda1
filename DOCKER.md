# Docker Setup for Mobile Carwash Server

This document provides comprehensive instructions for running the Mobile Carwash Server using Docker in different environments.

## 🏗️ Architecture Overview

The Docker setup includes:
- **Multi-stage Dockerfile** with development and production targets
- **Separate databases** for development and production environments
- **Environment-specific configurations**
- **Health checks** and monitoring
- **Security optimizations** for production

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Git

## 🚀 Quick Start

### Choose Your Database Setup

#### Option 1: Local Development (with local PostgreSQL)
```bash
# Clone and setup
git clone <repository-url>
cd mobile_carwash_server
cp .env.example .env

# Start with local database
npm run docker:dev

# Access services
# - API Server: http://localhost:8080
# - pgAdmin: http://localhost:5050
# - PostgreSQL: localhost:5432
```

#### Option 2: Cloud Development (with Supabase - Recommended)
```bash
# Clone and setup
git clone <repository-url>
cd mobile_carwash_server
cp .env.example .env

# Configure Supabase URL in .env:
# DB_URI=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres

# Start with cloud database
npm run docker:cloud

# Access services
# - API Server: http://localhost:8080
# - Supabase Dashboard: https://supabase.com/dashboard
```

**📖 For detailed Supabase setup, see [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)**

### Production Environment

1. **Set up production environment variables**
   ```bash
   # Create production environment file
   cp .env.example .env.prod
   # Edit .env.prod with production values
   ```

2. **Deploy to production**
   ```bash
   # Build and start production services
   docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

   # View production logs
   docker-compose -f docker-compose.prod.yml logs -f app
   ```

## 🔧 Configuration

### Environment Variables

Key environment variables for different environments:

#### Development
```env
NODE_ENV=development
DB_URI=********************************************************/mobile_carwash_dev
JWT_SECRET=dev-secret-key
STRIPE_SECRET_KEY=sk_test_...
```

#### Production
```env
NODE_ENV=production
DB_URI=***************************************************/mobile_carwash_prod
JWT_SECRET=super-secure-production-secret
STRIPE_SECRET_KEY=sk_live_...
```

### Database Configuration

#### Development Database
- **Host**: postgres (container name)
- **Port**: 5432
- **Database**: mobile_carwash_dev
- **User**: carwash_user
- **Password**: carwash_password

#### Production Database
- Uses environment variables for security
- No exposed ports for security
- Persistent volumes for data

## 📦 Docker Commands

### Building Images

```bash
# Build development image
docker build --target development -t mobile-carwash:dev .

# Build production image
docker build --target production -t mobile-carwash:prod .

# Build with specific platform
docker build --platform linux/amd64 -t mobile-carwash:prod .
```

### Managing Services

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart specific service
docker-compose restart app

# View service status
docker-compose ps

# View logs
docker-compose logs -f app
docker-compose logs postgres
```

### Database Operations

```bash
# Initialize database
docker-compose exec app npm run init-db

# Access PostgreSQL shell
docker-compose exec postgres psql -U carwash_user -d mobile_carwash_dev

# Backup database
docker-compose exec postgres pg_dump -U carwash_user mobile_carwash_dev > backup.sql

# Restore database
docker-compose exec -T postgres psql -U carwash_user mobile_carwash_dev < backup.sql
```

### Development Workflow

```bash
# Install new dependencies
docker-compose exec app npm install package-name

# Run tests
docker-compose exec app npm test

# Access container shell
docker-compose exec app sh

# Rebuild after package.json changes
docker-compose up --build app
```

## 🔍 Monitoring and Health Checks

### Health Check Endpoints

The application includes health checks:
- **Application**: `GET /api/health`
- **Database**: Automatic PostgreSQL health checks
- **Redis**: Automatic Redis health checks

### Viewing Health Status

```bash
# Check container health
docker-compose ps

# View health check logs
docker inspect mobile-carwash-api-dev | grep -A 10 Health
```

## 🛡️ Security Considerations

### Development
- Database ports exposed for development tools
- Source code mounted for hot reload
- Debug logging enabled

### Production
- No exposed database ports
- No source code mounts
- Resource limits applied
- Non-root user execution
- Minimal Alpine base image
- Log rotation configured

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the port
   lsof -i :8080

   # Change port in docker-compose.yml
   ports:
     - "8081:8080"
   ```

2. **Database connection issues**
   ```bash
   # Check database logs
   docker-compose logs postgres

   # Verify database is ready
   docker-compose exec postgres pg_isready -U carwash_user
   ```

3. **Permission issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .

   # Rebuild containers
   docker-compose down -v
   docker-compose up --build
   ```

4. **Out of disk space**
   ```bash
   # Clean up Docker resources
   docker system prune -a
   docker volume prune
   ```

### Debugging

```bash
# Access application logs
docker-compose logs -f app

# Access container shell for debugging
docker-compose exec app sh

# Check environment variables
docker-compose exec app env

# Monitor resource usage
docker stats
```

## 📊 Performance Optimization

### Production Optimizations
- Multi-stage builds reduce image size
- Alpine Linux base for minimal footprint
- Resource limits prevent resource exhaustion
- Health checks ensure service reliability
- Log rotation prevents disk space issues

### Development Optimizations
- Volume mounts for hot reload
- Separate development dependencies
- Database management tools included

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Build and Deploy
on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: docker build --target production -t mobile-carwash:latest .
      - name: Deploy to production
        run: |
          docker-compose -f docker-compose.prod.yml up -d
```

## 📝 Additional Resources

- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Hub](https://hub.docker.com/_/postgres)
- [Node.js Docker Best Practices](https://github.com/nodejs/docker-node/blob/main/docs/BestPractices.md)

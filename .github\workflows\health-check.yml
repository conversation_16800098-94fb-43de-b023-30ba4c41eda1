name: Health Check

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to check'
        required: true
        default: 'staging'
        type: choice
        options:
        - development
        - staging
      detailed:
        description: 'Run detailed health checks'
        required: false
        default: true
        type: boolean
  schedule:
    # Run health checks every 6 hours
    - cron: '0 */6 * * *'

jobs:
  health-check-dev:
    name: Development Health Check
    runs-on: ubuntu-latest
    if: github.event.inputs.environment == 'development' || github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.DEV_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.DEV_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Run health checks
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          echo "🏥 Running Development Environment Health Check..."
          echo "================================================"
          
          # Basic connectivity test
          echo "✅ SSH connection successful"
          
          # Check if Docker is running
          if sudo docker ps > /dev/null 2>&1; then
            echo "✅ Docker is running"
          else
            echo "❌ Docker is not running"
            exit 1
          fi
          
          # Check if application containers are running
          cd /opt/mobile-carwash || { echo "❌ Deployment directory not found"; exit 1; }
          
          if sudo docker-compose -f docker-compose.cloud.yml ps | grep -q "Up"; then
            echo "✅ Application containers are running"
          else
            echo "❌ Application containers are not running"
            sudo docker-compose -f docker-compose.cloud.yml ps
            exit 1
          fi
          
          # Test health endpoint
          if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
            echo "✅ Health endpoint responding"
          else
            echo "❌ Health endpoint not responding"
            exit 1
          fi
          
          # Check system resources
          echo ""
          echo "📊 System Resources:"
          echo "Memory usage: $(free -h | grep Mem | awk '{print $3"/"$2}')"
          echo "Disk usage: $(df -h / | tail -1 | awk '{print $5}')"
          echo "Load average: $(uptime | awk -F'load average:' '{print $2}')"
          
          # Check container logs for errors (last 50 lines)
          echo ""
          echo "📋 Recent logs (checking for errors):"
          if sudo docker-compose -f docker-compose.cloud.yml logs --tail=50 app | grep -i error; then
            echo "⚠️ Errors found in recent logs"
          else
            echo "✅ No errors in recent logs"
          fi
          
          echo ""
          echo "🎉 Development environment health check completed!"
        EOF
        
  health-check-staging:
    name: Staging Health Check
    runs-on: ubuntu-latest
    if: github.event.inputs.environment == 'staging' || github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.STAGING_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Run health checks
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          echo "🏥 Running Staging Environment Health Check..."
          echo "=============================================="
          
          # Basic connectivity test
          echo "✅ SSH connection successful"
          
          # Check if Docker is running
          if sudo docker ps > /dev/null 2>&1; then
            echo "✅ Docker is running"
          else
            echo "❌ Docker is not running"
            exit 1
          fi
          
          # Check if application containers are running
          cd /opt/mobile-carwash || { echo "❌ Deployment directory not found"; exit 1; }
          
          if sudo docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
            echo "✅ Application containers are running"
          else
            echo "❌ Application containers are not running"
            sudo docker-compose -f docker-compose.prod.yml ps
            exit 1
          fi
          
          # Test health endpoint
          if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
            echo "✅ Health endpoint responding"
          else
            echo "❌ Health endpoint not responding"
            exit 1
          fi
          
          # Test API endpoints (if detailed check requested)
          if [ "${{ github.event.inputs.detailed }}" = "true" ] || [ "${{ github.event_name }}" = "schedule" ]; then
            echo ""
            echo "🔍 Running detailed API checks..."
            
            # Test various endpoints
            endpoints=("/api/health" "/api/auth/health" "/api/users/health" "/api/services/health")
            
            for endpoint in "${endpoints[@]}"; do
              if curl -f "http://localhost:8080$endpoint" > /dev/null 2>&1; then
                echo "✅ $endpoint responding"
              else
                echo "⚠️ $endpoint not responding (may not be implemented)"
              fi
            done
          fi
          
          # Check system resources
          echo ""
          echo "📊 System Resources:"
          echo "Memory usage: $(free -h | grep Mem | awk '{print $3"/"$2}')"
          echo "Disk usage: $(df -h / | tail -1 | awk '{print $5}')"
          echo "Load average: $(uptime | awk -F'load average:' '{print $2}')"
          
          # Check Docker stats
          echo ""
          echo "🐳 Container Resources:"
          sudo docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
          
          # Check container logs for errors (last 100 lines)
          echo ""
          echo "📋 Recent logs (checking for errors):"
          if sudo docker-compose -f docker-compose.prod.yml logs --tail=100 app | grep -i error; then
            echo "⚠️ Errors found in recent logs"
          else
            echo "✅ No errors in recent logs"
          fi
          
          # Check backup status
          echo ""
          echo "💾 Backup Status:"
          if [ -d "backups" ] && [ "$(ls -A backups)" ]; then
            echo "✅ Backups directory exists and contains files"
            echo "Latest backup: $(ls -t backups/*.tar.gz 2>/dev/null | head -1 | xargs basename 2>/dev/null || echo 'No backups found')"
          else
            echo "⚠️ No backups found"
          fi
          
          echo ""
          echo "🎉 Staging environment health check completed!"
        EOF
        
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    if: github.event.inputs.detailed == 'true' && github.event.inputs.environment == 'staging'
    needs: health-check-staging
    
    steps:
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.STAGING_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Run basic load test
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          echo "🚀 Running basic performance test..."
          
          # Simple load test using curl
          echo "Testing with 10 concurrent requests..."
          
          start_time=$(date +%s)
          
          for i in {1..10}; do
            (
              for j in {1..5}; do
                curl -s http://localhost:8080/api/health > /dev/null
                if [ $? -eq 0 ]; then
                  echo "Request $((i*j)) successful"
                else
                  echo "Request $((i*j)) failed"
                fi
              done
            ) &
          done
          
          wait
          
          end_time=$(date +%s)
          duration=$((end_time - start_time))
          
          echo "✅ Load test completed in ${duration} seconds"
          echo "📊 50 requests processed with 10 concurrent connections"
        EOF

  summary:
    name: Health Check Summary
    runs-on: ubuntu-latest
    needs: [health-check-dev, health-check-staging, performance-test]
    if: always()
    
    steps:
    - name: Generate summary
      run: |
        echo "🏥 Health Check Summary"
        echo "======================"
        echo ""
        
        if [ "${{ needs.health-check-dev.result }}" = "success" ]; then
          echo "✅ Development environment: Healthy"
        elif [ "${{ needs.health-check-dev.result }}" = "skipped" ]; then
          echo "⏭️ Development environment: Skipped"
        else
          echo "❌ Development environment: Issues detected"
        fi
        
        if [ "${{ needs.health-check-staging.result }}" = "success" ]; then
          echo "✅ Staging environment: Healthy"
        elif [ "${{ needs.health-check-staging.result }}" = "skipped" ]; then
          echo "⏭️ Staging environment: Skipped"
        else
          echo "❌ Staging environment: Issues detected"
        fi
        
        if [ "${{ needs.performance-test.result }}" = "success" ]; then
          echo "✅ Performance test: Passed"
        elif [ "${{ needs.performance-test.result }}" = "skipped" ]; then
          echo "⏭️ Performance test: Skipped"
        else
          echo "⚠️ Performance test: Issues detected"
        fi
        
        echo ""
        echo "🕐 Check completed at: $(date)"
        
        # Set overall status
        if [ "${{ needs.health-check-dev.result }}" = "failure" ] || [ "${{ needs.health-check-staging.result }}" = "failure" ]; then
          echo "❌ Overall status: Action required"
          exit 1
        else
          echo "✅ Overall status: All systems operational"
        fi
